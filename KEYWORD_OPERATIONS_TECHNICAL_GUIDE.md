# FlexProxy 基于关键字的智能修改功能 - 技术实现指南

## 🎯 功能概述

FlexProxy现在支持基于关键字的智能修改功能，可以对HTTP请求和响应进行精确的、基于模式匹配的修改操作。

## 🏗️ 技术架构

### 核心数据结构

#### 1. 匹配类型 (MatchType)
```go
type MatchType string

const (
    MatchTypeExact     MatchType = "exact"     // 精确匹配
    MatchTypeContains  MatchType = "contains"  // 包含匹配
    MatchTypeWildcard  MatchType = "wildcard"  // 通配符匹配 (*, ?)
    MatchTypeRegex     MatchType = "regex"     // 正则表达式匹配
)
```

#### 2. 操作类型 (OperationType)
```go
type OperationType string

const (
    OperationTypeAdd     OperationType = "add"     // 添加
    OperationTypeReplace OperationType = "replace" // 替换
    OperationTypeRemove  OperationType = "remove"  // 删除
    OperationTypeAppend  OperationType = "append"  // 追加
)
```

#### 3. 条件类型 (ConditionType)
```go
type ConditionType string

const (
    ConditionExists      ConditionType = "exists"       // 存在
    ConditionNotExists   ConditionType = "not_exists"   // 不存在
    ConditionValueMatch  ConditionType = "value_match"  // 值匹配
)
```

#### 4. Header关键字操作
```go
type HeaderKeywordOperation struct {
    Operation     OperationType `json:"operation"`         // 操作类型
    MatchType     MatchType     `json:"match_type"`        // 匹配类型
    Pattern       string        `json:"pattern"`           // 匹配模式
    ValuePattern  string        `json:"value_pattern"`     // 值匹配模式
    Replacement   string        `json:"replacement"`       // 替换值
    NewValue      string        `json:"new_value"`         // 新值
    Condition     ConditionType `json:"condition"`         // 条件
    CaseSensitive bool          `json:"case_sensitive"`    // 大小写敏感
}
```

#### 5. Body关键字操作
```go
type BodyKeywordOperation struct {
    Operation         OperationType `json:"operation"`           // 操作类型
    Format            string        `json:"format"`              // 内容格式
    MatchType         MatchType     `json:"match_type"`          // 匹配类型
    Pattern           string        `json:"pattern"`             // 匹配模式
    Replacement       string        `json:"replacement"`         // 替换值
    CaseSensitive     bool          `json:"case_sensitive"`      // 大小写敏感
    PreserveStructure bool          `json:"preserve_structure"`  // 保持结构完整性
    JSONPath          string        `json:"json_path"`           // JSON路径（可选）
}
```

### 核心处理引擎

#### 1. 模式匹配引擎
```go
func matchPattern(text, pattern string, matchType MatchType, caseSensitive bool) (bool, error)
```
- **精确匹配**: 完全相等比较
- **包含匹配**: 子字符串查找
- **通配符匹配**: 使用filepath.Match，支持`*`和`?`
- **正则表达式匹配**: 使用regexp.MatchString

#### 2. Header处理引擎
```go
func processHeaderKeywordOperations(header http.Header, operations []HeaderKeywordOperation, logger interfaces.LogService) error
```

**支持的操作**：
- **添加**: 添加新的Header（支持条件判断）
- **替换**: 替换Header名称或值（支持正则表达式）
- **删除**: 删除匹配的Header（支持通配符）
- **追加**: 在现有Header值后追加内容

#### 3. Body处理引擎
```go
func processBodyKeywordOperations(content string, operations []BodyKeywordOperation, logger interfaces.LogService) (string, error)
```

**支持的格式**：
- **JSON**: 结构化替换，保持JSON语法正确性
- **XML**: 字符串级别替换（可扩展为结构化）
- **HTML**: 字符串级别替换，注意标签完整性
- **Text**: 纯文本替换，支持所有匹配类型

## 📝 配置语法

### 基本语法结构
```yaml
keyword_operations:
  headers:
    - operation: "remove|replace|add|append"
      match_type: "exact|contains|wildcard|regex"
      pattern: "匹配模式"
      value_pattern: "值匹配模式"  # 可选
      replacement: "替换内容"     # 可选
      new_value: "新值"          # 可选
      condition: "exists|not_exists|value_match"  # 可选
      case_sensitive: true|false  # 可选，默认false
  
  body:
    - operation: "remove|replace|add|append"
      format: "json|xml|html|text|auto"
      match_type: "exact|contains|wildcard|regex"
      pattern: "匹配模式"
      replacement: "替换内容"
      case_sensitive: true|false      # 可选，默认false
      preserve_structure: true|false  # 可选，默认false
```

### Header操作示例

#### 1. 删除跟踪Header
```yaml
keyword_operations:
  headers:
    - operation: "remove"
      match_type: "wildcard"
      pattern: "X-Tracking-*"
      case_sensitive: false
```

#### 2. 替换User-Agent
```yaml
keyword_operations:
  headers:
    - operation: "replace"
      match_type: "exact"
      pattern: "User-Agent"
      value_pattern: ".*Chrome.*"
      replacement: "FlexProxy-Browser/1.0"
      condition: "value_match"
```

#### 3. 条件性添加Header
```yaml
keyword_operations:
  headers:
    - operation: "add"
      pattern: "X-Frame-Options"
      new_value: "DENY"
      condition: "not_exists"
```

### Body操作示例

#### 1. JSON敏感信息脱敏
```yaml
keyword_operations:
  body:
    - operation: "replace"
      format: "json"
      match_type: "exact"
      pattern: "password"
      replacement: "***MASKED***"
      preserve_structure: true
```

#### 2. 正则表达式替换
```yaml
keyword_operations:
  body:
    - operation: "replace"
      format: "text"
      match_type: "regex"
      pattern: "\\b\\d{4}-\\d{4}-\\d{4}-\\d{4}\\b"
      replacement: "****-****-****-****"
```

#### 3. HTML内容修改
```yaml
keyword_operations:
  body:
    - operation: "replace"
      format: "html"
      match_type: "regex"
      pattern: "<title>(.*?)</title>"
      replacement: "<title>$1 - 由FlexProxy代理</title>"
```

## 🔧 实现细节

### 1. 执行顺序
1. **关键字操作** (keyword_operations) - 最高优先级
2. **传统Header修改** (headers, remove_headers)
3. **Body修改** (body, body_config)

### 2. JSON结构化处理
- 使用`json.Marshal`和`json.Unmarshal`保持JSON结构
- 递归遍历JSON对象，匹配键名和值
- 支持嵌套对象和数组的处理
- 自动保持JSON语法正确性

### 3. 通配符匹配
- 使用Go标准库`filepath.Match`
- 支持`*`（匹配任意字符）和`?`（匹配单个字符）
- 示例：`X-Tracking-*`匹配`X-Tracking-ID`、`X-Tracking-Token`等

### 4. 正则表达式支持
- 使用Go标准库`regexp`
- 支持捕获组和替换
- 示例：`(\\d{4})-(\\d{4})-(\\d{4})-(\\d{4})`可以替换为`$1-****-****-$4`

### 5. 大小写处理
- 默认大小写不敏感
- 可通过`case_sensitive`参数控制
- 大小写不敏感替换使用自定义算法确保正确性

## 🚀 性能优化

### 1. 正则表达式缓存
- 预编译正则表达式避免重复编译
- 使用sync.Map缓存编译结果

### 2. 批量操作
- 支持多个操作的顺序执行
- 减少HTTP对象的重复读写

### 3. 内存管理
- 使用io.NopCloser避免不必要的内存复制
- 及时释放原始Body资源

## ⚠️ 注意事项

### 1. 性能考虑
- 大文件的Body修改会消耗较多内存
- 复杂正则表达式可能影响性能
- JSON结构化处理比字符串替换慢

### 2. 安全考虑
- 正则表达式可能存在ReDoS攻击风险
- 建议对正则表达式进行验证和限制
- 敏感信息替换要确保完全覆盖

### 3. 兼容性
- 保持与现有配置的完全兼容
- 关键字操作优先级高于传统操作
- 错误处理不影响其他功能

## 📊 测试验证

### 功能测试覆盖
- ✅ Header通配符匹配和删除
- ✅ Header正则表达式替换
- ✅ Header条件性操作
- ✅ JSON结构化替换
- ✅ 文本正则表达式替换
- ✅ 大小写敏感/不敏感处理
- ✅ 复合操作执行
- ✅ 错误处理和日志记录

### 性能测试
- 大文件处理性能
- 复杂正则表达式性能
- 内存使用情况
- 并发处理能力

## 🎯 使用建议

### 1. 选择合适的匹配类型
- **精确匹配**: 性能最好，适用于已知的固定值
- **包含匹配**: 适用于部分匹配场景
- **通配符匹配**: 适用于简单的模式匹配
- **正则表达式**: 功能最强大，但性能开销最大

### 2. 优化配置
- 将最常用的操作放在前面
- 使用精确匹配而非正则表达式（如果可能）
- 合理使用`preserve_structure`选项

### 3. 安全实践
- 验证正则表达式的安全性
- 对敏感信息替换进行充分测试
- 监控性能指标和错误日志

FlexProxy的基于关键字的智能修改功能为HTTP代理提供了强大而灵活的内容处理能力！
