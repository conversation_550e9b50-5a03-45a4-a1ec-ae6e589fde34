# FlexProxy 基于关键字的智能修改功能配置示例
# 支持Header和Body的高级关键字操作

global:
  enable: true
  dns_cache_ttl: 300

# 定义基于关键字的智能修改动作
actions:
  # 1. Header关键字操作示例
  # ================================================
  
  # 1.1 删除所有跟踪相关的Header
  remove_tracking_headers:
    sequence:
      - type: "modify_request"
        keyword_operations:
          headers:
            - operation: "remove"
              match_type: "wildcard"
              pattern: "X-Tracking-*"
              case_sensitive: false
            - operation: "remove"
              match_type: "contains"
              pattern: "tracking"
              case_sensitive: false
            - operation: "remove"
              match_type: "regex"
              pattern: ".*Analytics.*"
              case_sensitive: false

  # 1.2 替换User-Agent中的浏览器信息
  modify_user_agent:
    sequence:
      - type: "modify_request"
        keyword_operations:
          headers:
            - operation: "replace"
              match_type: "exact"
              pattern: "User-Agent"
              value_pattern: ".*Chrome.*"
              replacement: "FlexProxy-Browser/1.0"
              condition: "value_match"
              case_sensitive: false

  # 1.3 添加安全Header（仅当不存在时）
  add_security_headers:
    sequence:
      - type: "modify_response"
        keyword_operations:
          headers:
            - operation: "add"
              pattern: "X-Frame-Options"
              new_value: "DENY"
              condition: "not_exists"
            - operation: "add"
              pattern: "X-Content-Type-Options"
              new_value: "nosniff"
              condition: "not_exists"
            - operation: "add"
              pattern: "X-XSS-Protection"
              new_value: "1; mode=block"
              condition: "not_exists"

  # 1.4 修改所有以X-开头的自定义Header
  modify_custom_headers:
    sequence:
      - type: "modify_response"
        keyword_operations:
          headers:
            - operation: "replace"
              match_type: "wildcard"
              pattern: "X-*"
              value_pattern: ".*"
              replacement: "Modified-by-FlexProxy"
              condition: "exists"

  # 2. Body关键字操作示例
  # ================================================

  # 2.1 JSON格式敏感信息脱敏
  json_data_masking:
    sequence:
      - type: "modify_response"
        keyword_operations:
          body:
            - operation: "replace"
              format: "json"
              match_type: "exact"
              pattern: "password"
              replacement: "***MASKED***"
              preserve_structure: true
              case_sensitive: false
            - operation: "replace"
              format: "json"
              match_type: "exact"
              pattern: "credit_card"
              replacement: "****-****-****-****"
              preserve_structure: true
            - operation: "replace"
              format: "json"
              match_type: "regex"
              pattern: "\\b\\d{4}-\\d{4}-\\d{4}-\\d{4}\\b"
              replacement: "****-****-****-****"
              preserve_structure: true

  # 2.2 HTML内容关键字替换
  html_content_replacement:
    sequence:
      - type: "modify_response"
        keyword_operations:
          body:
            - operation: "replace"
              format: "html"
              match_type: "contains"
              pattern: "原始网站"
              replacement: "FlexProxy代理网站"
              case_sensitive: false
            - operation: "replace"
              format: "html"
              match_type: "regex"
              pattern: "<title>(.*?)</title>"
              replacement: "<title>$1 - 由FlexProxy代理</title>"

  # 2.3 纯文本内容过滤
  text_content_filtering:
    sequence:
      - type: "modify_response"
        keyword_operations:
          body:
            - operation: "replace"
              format: "text"
              match_type: "contains"
              pattern: "敏感词汇"
              replacement: "***"
              case_sensitive: false
            - operation: "remove"
              format: "text"
              match_type: "regex"
              pattern: "\\b\\d{3}-\\d{2}-\\d{4}\\b"  # 社会安全号码格式

  # 2.4 XML数据修改
  xml_data_modification:
    sequence:
      - type: "modify_request"
        keyword_operations:
          body:
            - operation: "replace"
              format: "xml"
              match_type: "contains"
              pattern: "<password>"
              replacement: "<password>***HIDDEN***</password>"
            - operation: "replace"
              format: "xml"
              match_type: "regex"
              pattern: "<user_id>(.*?)</user_id>"
              replacement: "<user_id>ANONYMOUS</user_id>"

  # 3. 复合操作示例
  # ================================================

  # 3.1 完整的隐私保护操作
  privacy_protection:
    sequence:
      - type: "modify_request"
        keyword_operations:
          headers:
            # 删除跟踪Header
            - operation: "remove"
              match_type: "wildcard"
              pattern: "X-Tracking-*"
            - operation: "remove"
              match_type: "contains"
              pattern: "Analytics"
              case_sensitive: false
            # 修改User-Agent
            - operation: "replace"
              match_type: "exact"
              pattern: "User-Agent"
              value_pattern: ".*"
              replacement: "FlexProxy-Privacy/1.0"
          body:
            # JSON数据脱敏
            - operation: "replace"
              format: "json"
              match_type: "exact"
              pattern: "email"
              replacement: "<EMAIL>"
              preserve_structure: true
            - operation: "replace"
              format: "json"
              match_type: "exact"
              pattern: "phone"
              replacement: "***-***-****"
              preserve_structure: true

  # 3.2 API响应标准化
  api_response_standardization:
    sequence:
      - type: "modify_response"
        keyword_operations:
          headers:
            # 标准化响应Header
            - operation: "add"
              pattern: "X-API-Version"
              new_value: "v2.0"
              condition: "not_exists"
            - operation: "replace"
              match_type: "exact"
              pattern: "Server"
              value_pattern: ".*"
              replacement: "FlexProxy-API-Gateway"
          body:
            # 标准化JSON响应格式
            - operation: "replace"
              format: "json"
              match_type: "exact"
              pattern: "success"
              replacement: "status"
              preserve_structure: true
            - operation: "replace"
              format: "json"
              match_type: "exact"
              pattern: "data"
              replacement: "result"
              preserve_structure: true

  # 3.3 内容审查和过滤
  content_moderation:
    sequence:
      - type: "modify_response"
        keyword_operations:
          body:
            # 过滤敏感内容
            - operation: "replace"
              format: "text"
              match_type: "regex"
              pattern: "\\b(password|secret|token)\\s*[:=]\\s*\\S+"
              replacement: "[REDACTED]"
              case_sensitive: false
            # 替换外部链接
            - operation: "replace"
              format: "html"
              match_type: "regex"
              pattern: "https?://external\\.com"
              replacement: "https://proxy.internal.com"
            # JSON API密钥脱敏
            - operation: "replace"
              format: "json"
              match_type: "contains"
              pattern: "api_key"
              replacement: "***HIDDEN***"
              preserve_structure: true

  # 4. 高级匹配示例
  # ================================================

  # 4.1 条件性Header修改
  conditional_header_modification:
    sequence:
      - type: "modify_response"
        keyword_operations:
          headers:
            # 仅当Content-Type为JSON时添加CORS头
            - operation: "add"
              pattern: "Access-Control-Allow-Origin"
              new_value: "*"
              condition: "value_match"
              value_pattern: "application/json"
            # 仅当存在Cache-Control时修改它
            - operation: "replace"
              match_type: "exact"
              pattern: "Cache-Control"
              value_pattern: ".*"
              replacement: "no-cache, no-store, must-revalidate"
              condition: "exists"

  # 4.2 多层级JSON修改
  nested_json_modification:
    sequence:
      - type: "modify_response"
        keyword_operations:
          body:
            # 修改嵌套JSON中的用户信息
            - operation: "replace"
              format: "json"
              match_type: "exact"
              pattern: "user_name"
              replacement: "Anonymous"
              preserve_structure: true
            - operation: "replace"
              format: "json"
              match_type: "regex"
              pattern: "\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}\\b"
              replacement: "***@***.***"
              preserve_structure: true

# 定义事件触发规则
events:
  # 隐私保护事件
  - name: "privacy_protection_event"
    enable: true
    trigger_type: "url"
    process_stage: "pre_request"
    priority: 1
    conditions:
      - name: "privacy_url_condition"
        enable: true
        url_patterns:
          patterns: [".*/api/user/.*", ".*/profile/.*"]
          match_type: "regex"
    matches:
      - name: "privacy_match"
        enable: true
        conditions: ["privacy_url_condition"]
        actions:
          - type: "modify_request"
            keyword_operations:
              headers:
                - operation: "remove"
                  match_type: "wildcard"
                  pattern: "X-Tracking-*"
              body:
                - operation: "replace"
                  format: "json"
                  match_type: "exact"
                  pattern: "sensitive_data"
                  replacement: "***PROTECTED***"
                  preserve_structure: true

  # API响应标准化事件
  - name: "api_standardization_event"
    enable: true
    trigger_type: "status"
    process_stage: "post_header"
    priority: 2
    conditions:
      - name: "api_response_condition"
        enable: true
        status_codes:
          codes: [200, 201, 202]
          match_type: "include"
    matches:
      - name: "api_standardization_match"
        enable: true
        conditions: ["api_response_condition"]
        actions:
          - type: "modify_response"
            keyword_operations:
              headers:
                - operation: "add"
                  pattern: "X-API-Gateway"
                  new_value: "FlexProxy"
                  condition: "not_exists"
              body:
                - operation: "replace"
                  format: "json"
                  match_type: "exact"
                  pattern: "error_code"
                  replacement: "status_code"
                  preserve_structure: true
