package action

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"encoding/xml"
	"fmt"
	"io"
	"net/http"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/mubeng/mubeng/common"
	"github.com/mubeng/mubeng/common/constants"
	"github.com/mubeng/mubeng/common/errors"
	"github.com/mubeng/mubeng/common/logger"
	"github.com/mubeng/mubeng/internal/interfaces"
)

// =============================================================================
// Executor接口定义
// =============================================================================

// Executor 定义动作执行器的接口
type Executor interface {
	// Execute 执行动作
	Execute(ctx context.Context, parameters map[string]interface{}) error
	// Validate 验证参数
	Validate(parameters map[string]interface{}) error
	// GetType 获取执行器类型
	GetType() string
	// GetDescription 获取执行器描述
	GetDescription() string
}

// HTTPExecutor 定义可以处理HTTP请求/响应的执行器接口
type HTTPExecutor interface {
	Executor
	// ExecuteHTTP 执行HTTP相关动作，可以修改请求或响应
	ExecuteHTTP(ctx context.Context, parameters map[string]interface{}, req *http.Request, resp *http.Response) (*http.Request, *http.Response, error)
}

// =============================================================================
// 数据结构定义
// =============================================================================

// BodyConfig 定义body修改的详细配置
type BodyConfig struct {
	Content     string `json:"content" yaml:"content"`         // 内容
	ContentType string `json:"content_type" yaml:"content_type"` // MIME类型
	Format      string `json:"format" yaml:"format"`           // 格式：json, xml, html, text, form, binary
	Encoding    string `json:"encoding" yaml:"encoding"`       // 编码：utf-8, base64
}

// MatchType 定义匹配类型
type MatchType string

const (
	MatchTypeExact     MatchType = "exact"     // 精确匹配
	MatchTypeContains  MatchType = "contains"  // 包含匹配
	MatchTypeWildcard  MatchType = "wildcard"  // 通配符匹配
	MatchTypeRegex     MatchType = "regex"     // 正则表达式匹配
)

// OperationType 定义操作类型
type OperationType string

const (
	OperationTypeAdd     OperationType = "add"     // 添加
	OperationTypeReplace OperationType = "replace" // 替换
	OperationTypeRemove  OperationType = "remove"  // 删除
	OperationTypeAppend  OperationType = "append"  // 追加
)

// ConditionType 定义条件类型
type ConditionType string

const (
	ConditionExists      ConditionType = "exists"       // 存在
	ConditionNotExists   ConditionType = "not_exists"   // 不存在
	ConditionValueMatch  ConditionType = "value_match"  // 值匹配
)

// HeaderKeywordOperation 定义Header关键字操作
type HeaderKeywordOperation struct {
	Operation     OperationType `json:"operation" yaml:"operation"`         // 操作类型
	MatchType     MatchType     `json:"match_type" yaml:"match_type"`       // 匹配类型
	Pattern       string        `json:"pattern" yaml:"pattern"`             // 匹配模式
	ValuePattern  string        `json:"value_pattern" yaml:"value_pattern"` // 值匹配模式
	Replacement   string        `json:"replacement" yaml:"replacement"`     // 替换值
	NewValue      string        `json:"new_value" yaml:"new_value"`         // 新值
	Condition     ConditionType `json:"condition" yaml:"condition"`         // 条件
	CaseSensitive bool          `json:"case_sensitive" yaml:"case_sensitive"` // 大小写敏感
}

// BodyKeywordOperation 定义Body关键字操作
type BodyKeywordOperation struct {
	Operation         OperationType `json:"operation" yaml:"operation"`                   // 操作类型
	Format            string        `json:"format" yaml:"format"`                         // 内容格式
	MatchType         MatchType     `json:"match_type" yaml:"match_type"`                 // 匹配类型
	Pattern           string        `json:"pattern" yaml:"pattern"`                       // 匹配模式
	Replacement       string        `json:"replacement" yaml:"replacement"`               // 替换值
	CaseSensitive     bool          `json:"case_sensitive" yaml:"case_sensitive"`         // 大小写敏感
	PreserveStructure bool          `json:"preserve_structure" yaml:"preserve_structure"` // 保持结构完整性
	JSONPath          string        `json:"json_path" yaml:"json_path"`                   // JSON路径（可选）
}

// KeywordOperations 定义关键字操作集合
type KeywordOperations struct {
	Headers []HeaderKeywordOperation `json:"headers" yaml:"headers"` // Header操作列表
	Body    []BodyKeywordOperation   `json:"body" yaml:"body"`       // Body操作列表
}

// =============================================================================
// 关键字匹配引擎
// =============================================================================

// matchPattern 通用模式匹配函数
func matchPattern(text, pattern string, matchType MatchType, caseSensitive bool) (bool, error) {
	if !caseSensitive {
		text = strings.ToLower(text)
		pattern = strings.ToLower(pattern)
	}

	switch matchType {
	case MatchTypeExact:
		return text == pattern, nil
	case MatchTypeContains:
		return strings.Contains(text, pattern), nil
	case MatchTypeWildcard:
		matched, err := filepath.Match(pattern, text)
		return matched, err
	case MatchTypeRegex:
		matched, err := regexp.MatchString(pattern, text)
		return matched, err
	default:
		return false, errors.NewError(errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "不支持的匹配类型: "+string(matchType))
	}
}

// matchHeaderCondition 检查Header条件
func matchHeaderCondition(header http.Header, headerName string, condition ConditionType, valuePattern string, caseSensitive bool) bool {
	headerValue := header.Get(headerName)

	switch condition {
	case ConditionExists:
		return headerValue != ""
	case ConditionNotExists:
		return headerValue == ""
	case ConditionValueMatch:
		if valuePattern == "" {
			return true
		}
		matched, err := matchPattern(headerValue, valuePattern, MatchTypeRegex, caseSensitive)
		return err == nil && matched
	default:
		return true
	}
}

// =============================================================================
// 辅助函数
// =============================================================================

// getContentTypeByFormat 根据格式获取Content-Type
func getContentTypeByFormat(format string) string {
	switch strings.ToLower(format) {
	case "json":
		return "application/json"
	case "xml":
		return "application/xml"
	case "html":
		return "text/html"
	case "text", "plain":
		return "text/plain"
	case "form", "urlencoded":
		return "application/x-www-form-urlencoded"
	case "binary", "octet":
		return "application/octet-stream"
	default:
		return "text/plain"
	}
}

// detectContentType 自动检测内容类型
func detectContentType(content string) string {
	content = strings.TrimSpace(content)

	// 检测JSON
	if (strings.HasPrefix(content, "{") && strings.HasSuffix(content, "}")) ||
		(strings.HasPrefix(content, "[") && strings.HasSuffix(content, "]")) {
		var js json.RawMessage
		if json.Unmarshal([]byte(content), &js) == nil {
			return "application/json"
		}
	}

	// 检测XML
	if strings.HasPrefix(content, "<") && strings.HasSuffix(content, ">") {
		if strings.Contains(content, "<?xml") || strings.Contains(content, "<html") {
			if strings.Contains(content, "<html") {
				return "text/html"
			}
			return "application/xml"
		}
	}

	// 检测URL编码格式
	if strings.Contains(content, "=") && strings.Contains(content, "&") {
		return "application/x-www-form-urlencoded"
	}

	// 默认为纯文本
	return "text/plain"
}

// validateContent 验证内容格式
func validateContent(content, format string) error {
	switch strings.ToLower(format) {
	case "json":
		var js json.RawMessage
		if err := json.Unmarshal([]byte(content), &js); err != nil {
			return errors.NewError(errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "无效的JSON格式: "+err.Error())
		}
	case "xml":
		if err := xml.Unmarshal([]byte(content), &struct{}{}); err != nil {
			// XML验证比较复杂，这里只做基本检查
			if !strings.Contains(content, "<") || !strings.Contains(content, ">") {
				return errors.NewError(errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "无效的XML格式")
			}
		}
	}
	return nil
}

// validateBodyConfig 验证body_config参数
func validateBodyConfig(bodyConfigRaw interface{}, actionType string) error {
	bodyConfigMap, ok := bodyConfigRaw.(map[string]interface{})
	if !ok {
		return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
			fmt.Sprintf("%s动作的body_config参数必须是对象格式", actionType))
	}

	// 验证content字段（必需）
	contentRaw, hasContent := bodyConfigMap["content"]
	if !hasContent {
		return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldRequired,
			fmt.Sprintf("%s动作的body_config.content字段是必需的", actionType))
	}
	content, ok := contentRaw.(string)
	if !ok {
		return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
			fmt.Sprintf("%s动作的body_config.content必须是字符串", actionType))
	}

	// 验证format字段
	var format string
	if formatRaw, hasFormat := bodyConfigMap["format"]; hasFormat {
		var ok bool
		format, ok = formatRaw.(string)
		if !ok {
			return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				fmt.Sprintf("%s动作的body_config.format必须是字符串", actionType))
		}

		// 验证format的有效值
		validFormats := []string{"json", "xml", "html", "text", "plain", "form", "urlencoded", "binary", "octet", "auto"}
		isValidFormat := false
		formatLower := strings.ToLower(format)
		for _, validFormat := range validFormats {
			if formatLower == validFormat {
				isValidFormat = true
				break
			}
		}
		if !isValidFormat {
			return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				fmt.Sprintf("%s动作的body_config.format值无效: %s，支持的格式: %s",
					actionType, format, strings.Join(validFormats, ", ")))
		}
	} else {
		format = "auto" // 默认自动检测
	}

	// 验证encoding字段
	if encodingRaw, hasEncoding := bodyConfigMap["encoding"]; hasEncoding {
		encoding, ok := encodingRaw.(string)
		if !ok {
			return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				fmt.Sprintf("%s动作的body_config.encoding必须是字符串", actionType))
		}

		// 验证encoding的有效值
		validEncodings := []string{"utf-8", "base64"}
		isValidEncoding := false
		encodingLower := strings.ToLower(encoding)
		for _, validEncoding := range validEncodings {
			if encodingLower == validEncoding {
				isValidEncoding = true
				break
			}
		}
		if !isValidEncoding {
			return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				fmt.Sprintf("%s动作的body_config.encoding值无效: %s，支持的编码: %s",
					actionType, encoding, strings.Join(validEncodings, ", ")))
		}
	}

	// 验证content_type字段
	if contentTypeRaw, hasContentType := bodyConfigMap["content_type"]; hasContentType {
		if _, ok := contentTypeRaw.(string); !ok {
			return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				fmt.Sprintf("%s动作的body_config.content_type必须是字符串", actionType))
		}
	}

	// 验证auto_content_type字段
	if autoContentTypeRaw, hasAutoContentType := bodyConfigMap["auto_content_type"]; hasAutoContentType {
		if _, ok := autoContentTypeRaw.(bool); !ok {
			return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				fmt.Sprintf("%s动作的body_config.auto_content_type必须是布尔值", actionType))
		}
	}

	// 根据format验证content内容
	if format != "auto" && format != "binary" && format != "octet" {
		if err := validateContent(content, format); err != nil {
			return errors.WrapError(err, errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				fmt.Sprintf("%s动作的body_config.content格式验证失败", actionType))
		}
	}

	return nil
}

// validateKeywordOperations 验证keyword_operations参数
func validateKeywordOperations(keywordOpsRaw interface{}, actionType string) error {
	keywordOpsMap, ok := keywordOpsRaw.(map[string]interface{})
	if !ok {
		return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
			fmt.Sprintf("%s动作的keyword_operations参数必须是对象格式", actionType))
	}

	// 验证headers操作
	if headersRaw, hasHeaders := keywordOpsMap["headers"]; hasHeaders {
		if err := validateHeaderKeywordOperations(headersRaw, actionType); err != nil {
			return err
		}
	}

	// 验证body操作
	if bodyRaw, hasBody := keywordOpsMap["body"]; hasBody {
		if err := validateBodyKeywordOperations(bodyRaw, actionType); err != nil {
			return err
		}
	}

	return nil
}

// validateHeaderKeywordOperations 验证header关键字操作
func validateHeaderKeywordOperations(headersRaw interface{}, actionType string) error {
	headersList, ok := headersRaw.([]interface{})
	if !ok {
		return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
			fmt.Sprintf("%s动作的keyword_operations.headers必须是数组格式", actionType))
	}

	validOperations := []string{"add", "replace", "remove", "append"}
	validMatchTypes := []string{"exact", "contains", "wildcard", "regex"}
	validConditions := []string{"exists", "not_exists", "value_match"}

	for i, headerOpRaw := range headersList {
		headerOpMap, ok := headerOpRaw.(map[string]interface{})
		if !ok {
			return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				fmt.Sprintf("%s动作的keyword_operations.headers[%d]必须是对象格式", actionType, i))
		}

		// 验证operation字段（必需）
		operationRaw, hasOperation := headerOpMap["operation"]
		if !hasOperation {
			return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldRequired,
				fmt.Sprintf("%s动作的keyword_operations.headers[%d].operation字段是必需的", actionType, i))
		}
		operation, ok := operationRaw.(string)
		if !ok {
			return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				fmt.Sprintf("%s动作的keyword_operations.headers[%d].operation必须是字符串", actionType, i))
		}

		// 验证operation的有效值
		isValidOperation := false
		for _, validOp := range validOperations {
			if operation == validOp {
				isValidOperation = true
				break
			}
		}
		if !isValidOperation {
			return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				fmt.Sprintf("%s动作的keyword_operations.headers[%d].operation值无效: %s，支持的操作: %s",
					actionType, i, operation, strings.Join(validOperations, ", ")))
		}

		// 验证match_type字段
		if matchTypeRaw, hasMatchType := headerOpMap["match_type"]; hasMatchType {
			matchType, ok := matchTypeRaw.(string)
			if !ok {
				return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
					fmt.Sprintf("%s动作的keyword_operations.headers[%d].match_type必须是字符串", actionType, i))
			}

			// 验证match_type的有效值
			isValidMatchType := false
			for _, validMatchType := range validMatchTypes {
				if matchType == validMatchType {
					isValidMatchType = true
					break
				}
			}
			if !isValidMatchType {
				return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
					fmt.Sprintf("%s动作的keyword_operations.headers[%d].match_type值无效: %s，支持的匹配类型: %s",
						actionType, i, matchType, strings.Join(validMatchTypes, ", ")))
			}
		}

		// 验证condition字段
		if conditionRaw, hasCondition := headerOpMap["condition"]; hasCondition {
			condition, ok := conditionRaw.(string)
			if !ok {
				return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
					fmt.Sprintf("%s动作的keyword_operations.headers[%d].condition必须是字符串", actionType, i))
			}

			// 验证condition的有效值
			isValidCondition := false
			for _, validCondition := range validConditions {
				if condition == validCondition {
					isValidCondition = true
					break
				}
			}
			if !isValidCondition {
				return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
					fmt.Sprintf("%s动作的keyword_operations.headers[%d].condition值无效: %s，支持的条件: %s",
						actionType, i, condition, strings.Join(validConditions, ", ")))
			}
		}

		// 验证pattern字段（某些操作需要）
		if operation != "remove" {
			if patternRaw, hasPattern := headerOpMap["pattern"]; hasPattern {
				if _, ok := patternRaw.(string); !ok {
					return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
						fmt.Sprintf("%s动作的keyword_operations.headers[%d].pattern必须是字符串", actionType, i))
				}
			}
		}
	}

	return nil
}

// validateBodyKeywordOperations 验证body关键字操作
func validateBodyKeywordOperations(bodyRaw interface{}, actionType string) error {
	bodyList, ok := bodyRaw.([]interface{})
	if !ok {
		return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
			fmt.Sprintf("%s动作的keyword_operations.body必须是数组格式", actionType))
	}

	validOperations := []string{"replace", "remove", "add", "append"}
	validMatchTypes := []string{"exact", "contains", "wildcard", "regex"}
	validFormats := []string{"json", "xml", "html", "text", "auto"}

	for i, bodyOpRaw := range bodyList {
		bodyOpMap, ok := bodyOpRaw.(map[string]interface{})
		if !ok {
			return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				fmt.Sprintf("%s动作的keyword_operations.body[%d]必须是对象格式", actionType, i))
		}

		// 验证operation字段（必需）
		operationRaw, hasOperation := bodyOpMap["operation"]
		if !hasOperation {
			return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldRequired,
				fmt.Sprintf("%s动作的keyword_operations.body[%d].operation字段是必需的", actionType, i))
		}
		operation, ok := operationRaw.(string)
		if !ok {
			return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				fmt.Sprintf("%s动作的keyword_operations.body[%d].operation必须是字符串", actionType, i))
		}

		// 验证operation的有效值
		isValidOperation := false
		for _, validOp := range validOperations {
			if operation == validOp {
				isValidOperation = true
				break
			}
		}
		if !isValidOperation {
			return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				fmt.Sprintf("%s动作的keyword_operations.body[%d].operation值无效: %s，支持的操作: %s",
					actionType, i, operation, strings.Join(validOperations, ", ")))
		}

		// 验证format字段
		if formatRaw, hasFormat := bodyOpMap["format"]; hasFormat {
			format, ok := formatRaw.(string)
			if !ok {
				return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
					fmt.Sprintf("%s动作的keyword_operations.body[%d].format必须是字符串", actionType, i))
			}

			// 验证format的有效值
			isValidFormat := false
			for _, validFormat := range validFormats {
				if format == validFormat {
					isValidFormat = true
					break
				}
			}
			if !isValidFormat {
				return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
					fmt.Sprintf("%s动作的keyword_operations.body[%d].format值无效: %s，支持的格式: %s",
						actionType, i, format, strings.Join(validFormats, ", ")))
			}
		}

		// 验证match_type字段
		if matchTypeRaw, hasMatchType := bodyOpMap["match_type"]; hasMatchType {
			matchType, ok := matchTypeRaw.(string)
			if !ok {
				return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
					fmt.Sprintf("%s动作的keyword_operations.body[%d].match_type必须是字符串", actionType, i))
			}

			// 验证match_type的有效值
			isValidMatchType := false
			for _, validMatchType := range validMatchTypes {
				if matchType == validMatchType {
					isValidMatchType = true
					break
				}
			}
			if !isValidMatchType {
				return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
					fmt.Sprintf("%s动作的keyword_operations.body[%d].match_type值无效: %s，支持的匹配类型: %s",
						actionType, i, matchType, strings.Join(validMatchTypes, ", ")))
			}
		}

		// 验证pattern字段（必需）
		if patternRaw, hasPattern := bodyOpMap["pattern"]; hasPattern {
			if _, ok := patternRaw.(string); !ok {
				return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
					fmt.Sprintf("%s动作的keyword_operations.body[%d].pattern必须是字符串", actionType, i))
			}
		} else {
			return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldRequired,
				fmt.Sprintf("%s动作的keyword_operations.body[%d].pattern字段是必需的", actionType, i))
		}

		// 验证replacement字段（replace和add操作需要）
		if operation == "replace" || operation == "add" || operation == "append" {
			if replacementRaw, hasReplacement := bodyOpMap["replacement"]; hasReplacement {
				if _, ok := replacementRaw.(string); !ok {
					return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
						fmt.Sprintf("%s动作的keyword_operations.body[%d].replacement必须是字符串", actionType, i))
				}
			} else {
				return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldRequired,
					fmt.Sprintf("%s动作的keyword_operations.body[%d].replacement字段对于%s操作是必需的", actionType, i, operation))
			}
		}

		// 验证case_sensitive字段
		if caseSensitiveRaw, hasCaseSensitive := bodyOpMap["case_sensitive"]; hasCaseSensitive {
			if _, ok := caseSensitiveRaw.(bool); !ok {
				return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
					fmt.Sprintf("%s动作的keyword_operations.body[%d].case_sensitive必须是布尔值", actionType, i))
			}
		}

		// 验证preserve_structure字段
		if preserveStructureRaw, hasPreserveStructure := bodyOpMap["preserve_structure"]; hasPreserveStructure {
			if _, ok := preserveStructureRaw.(bool); !ok {
				return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
					fmt.Sprintf("%s动作的keyword_operations.body[%d].preserve_structure必须是布尔值", actionType, i))
			}
		}
	}

	return nil
}

// processHeaderKeywordOperations 处理Header关键字操作
func processHeaderKeywordOperations(header http.Header, operations []HeaderKeywordOperation, logger interfaces.LogService) error {
	for _, op := range operations {
		logger.Debug("执行Header关键字操作: operation=%s, pattern=%s, match_type=%s", op.Operation, op.Pattern, op.MatchType)

		switch op.Operation {
		case OperationTypeAdd:
			err := processHeaderAdd(header, op, logger)
			if err != nil {
				return err
			}
		case OperationTypeReplace:
			err := processHeaderReplace(header, op, logger)
			if err != nil {
				return err
			}
		case OperationTypeRemove:
			err := processHeaderRemove(header, op, logger)
			if err != nil {
				return err
			}
		case OperationTypeAppend:
			err := processHeaderAppend(header, op, logger)
			if err != nil {
				return err
			}
		default:
			return errors.NewError(errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "不支持的Header操作类型: "+string(op.Operation))
		}
	}
	return nil
}

// processHeaderAdd 处理Header添加操作
func processHeaderAdd(header http.Header, op HeaderKeywordOperation, logger interfaces.LogService) error {
	// 检查条件
	if !matchHeaderCondition(header, op.Pattern, op.Condition, op.ValuePattern, op.CaseSensitive) {
		logger.Debug("Header添加操作条件不满足: %s", op.Pattern)
		return nil
	}

	header.Set(op.Pattern, op.NewValue)
	logger.Debug("添加Header: %s = %s", op.Pattern, op.NewValue)
	return nil
}

// processHeaderReplace 处理Header替换操作
func processHeaderReplace(header http.Header, op HeaderKeywordOperation, logger interfaces.LogService) error {
	var headersToReplace []string

	// 查找匹配的headers
	for headerName := range header {
		matched, err := matchPattern(headerName, op.Pattern, op.MatchType, op.CaseSensitive)
		if err != nil {
			return errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "Header名称匹配失败")
		}

		if matched {
			// 检查条件
			if matchHeaderCondition(header, headerName, op.Condition, op.ValuePattern, op.CaseSensitive) {
				headersToReplace = append(headersToReplace, headerName)
			}
		}
	}

	// 执行替换
	for _, headerName := range headersToReplace {
		if op.ValuePattern != "" {
			// 替换header值
			oldValue := header.Get(headerName)
			var newValue string
			if op.MatchType == MatchTypeRegex {
				re, err := regexp.Compile(op.ValuePattern)
				if err != nil {
					return errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "正则表达式编译失败")
				}
				newValue = re.ReplaceAllString(oldValue, op.Replacement)
			} else {
				if op.CaseSensitive {
					newValue = strings.ReplaceAll(oldValue, op.ValuePattern, op.Replacement)
				} else {
					// 大小写不敏感替换
					lowerPattern := strings.ToLower(op.ValuePattern)
					newValue = oldValue
					for {
						index := strings.Index(strings.ToLower(newValue), lowerPattern)
						if index == -1 {
							break
						}
						newValue = newValue[:index] + op.Replacement + newValue[index+len(op.ValuePattern):]
					}
				}
			}
			header.Set(headerName, newValue)
			logger.Debug("替换Header值: %s = %s -> %s", headerName, oldValue, newValue)
		} else {
			// 替换整个header
			header.Set(headerName, op.Replacement)
			logger.Debug("替换Header: %s = %s", headerName, op.Replacement)
		}
	}

	return nil
}

// processHeaderRemove 处理Header删除操作
func processHeaderRemove(header http.Header, op HeaderKeywordOperation, logger interfaces.LogService) error {
	var headersToRemove []string

	// 查找匹配的headers
	for headerName := range header {
		matched, err := matchPattern(headerName, op.Pattern, op.MatchType, op.CaseSensitive)
		if err != nil {
			return errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "Header名称匹配失败")
		}

		if matched {
			// 检查条件
			if matchHeaderCondition(header, headerName, op.Condition, op.ValuePattern, op.CaseSensitive) {
				headersToRemove = append(headersToRemove, headerName)
			}
		}
	}

	// 执行删除
	for _, headerName := range headersToRemove {
		header.Del(headerName)
		logger.Debug("删除Header: %s", headerName)
	}

	return nil
}

// processHeaderAppend 处理Header追加操作
func processHeaderAppend(header http.Header, op HeaderKeywordOperation, logger interfaces.LogService) error {
	// 检查条件
	if !matchHeaderCondition(header, op.Pattern, op.Condition, op.ValuePattern, op.CaseSensitive) {
		logger.Debug("Header追加操作条件不满足: %s", op.Pattern)
		return nil
	}

	oldValue := header.Get(op.Pattern)
	newValue := oldValue + op.NewValue
	header.Set(op.Pattern, newValue)
	logger.Debug("追加Header: %s = %s -> %s", op.Pattern, oldValue, newValue)
	return nil
}

// modifyHTTPHeaders 修改HTTP头部（保持向后兼容）
func modifyHTTPHeaders(header http.Header, headersToAdd map[string]interface{}, headersToRemove []string) {
	// 添加或修改头部
	if headersToAdd != nil {
		for key, value := range headersToAdd {
			if strValue, ok := value.(string); ok {
				header.Set(key, strValue)
			}
		}
	}

	// 删除指定头部
	if headersToRemove != nil {
		for _, key := range headersToRemove {
			header.Del(key)
		}
	}
}

// processBodyKeywordOperations 处理Body关键字操作
func processBodyKeywordOperations(content string, operations []BodyKeywordOperation, logger interfaces.LogService) (string, error) {
	result := content

	for _, op := range operations {
		logger.Debug("执行Body关键字操作: operation=%s, pattern=%s, format=%s", op.Operation, op.Pattern, op.Format)

		var err error
		switch op.Operation {
		case OperationTypeReplace:
			result, err = processBodyReplace(result, op, logger)
		case OperationTypeRemove:
			result, err = processBodyRemove(result, op, logger)
		case OperationTypeAdd:
			result, err = processBodyAdd(result, op, logger)
		case OperationTypeAppend:
			result, err = processBodyAppend(result, op, logger)
		default:
			return result, errors.NewError(errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "不支持的Body操作类型: "+string(op.Operation))
		}

		if err != nil {
			return result, err
		}
	}

	return result, nil
}

// processBodyReplace 处理Body替换操作
func processBodyReplace(content string, op BodyKeywordOperation, logger interfaces.LogService) (string, error) {
	format := strings.ToLower(op.Format)
	if format == "" || format == "auto" {
		format = strings.ToLower(detectContentType(content))
		if strings.Contains(format, "json") {
			format = "json"
		} else if strings.Contains(format, "xml") {
			format = "xml"
		} else if strings.Contains(format, "html") {
			format = "html"
		} else {
			format = "text"
		}
	}

	switch format {
	case "json":
		return processJSONReplace(content, op, logger)
	case "xml":
		return processXMLReplace(content, op, logger)
	case "html":
		return processHTMLReplace(content, op, logger)
	case "text", "plain":
		return processTextReplace(content, op, logger)
	default:
		return processTextReplace(content, op, logger)
	}
}

// processJSONReplace 处理JSON格式的替换
func processJSONReplace(content string, op BodyKeywordOperation, logger interfaces.LogService) (string, error) {
	if op.PreserveStructure {
		// 保持JSON结构的替换
		var jsonData interface{}
		if err := json.Unmarshal([]byte(content), &jsonData); err != nil {
			return content, errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "JSON解析失败")
		}

		// 递归替换JSON中的值
		modifiedData := replaceInJSONValue(jsonData, op)

		// 重新序列化
		modifiedBytes, err := json.Marshal(modifiedData)
		if err != nil {
			return content, errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "JSON序列化失败")
		}

		result := string(modifiedBytes)
		logger.Debug("JSON结构化替换完成")
		return result, nil
	} else {
		// 简单字符串替换
		return processTextReplace(content, op, logger)
	}
}

// replaceInJSONValue 递归替换JSON值
func replaceInJSONValue(value interface{}, op BodyKeywordOperation) interface{} {
	switch v := value.(type) {
	case string:
		// 对字符串值进行匹配和替换
		if op.MatchType == MatchTypeRegex {
			re, err := regexp.Compile(op.Pattern)
			if err == nil {
				return re.ReplaceAllString(v, op.Replacement)
			}
		} else {
			matched, _ := matchPattern(v, op.Pattern, op.MatchType, op.CaseSensitive)
			if matched {
				if op.CaseSensitive {
					return strings.ReplaceAll(v, op.Pattern, op.Replacement)
				} else {
					// 大小写不敏感替换
					lowerPattern := strings.ToLower(op.Pattern)
					result := v
					for {
						index := strings.Index(strings.ToLower(result), lowerPattern)
						if index == -1 {
							break
						}
						result = result[:index] + op.Replacement + result[index+len(op.Pattern):]
					}
					return result
				}
			}
		}
		return v
	case map[string]interface{}:
		result := make(map[string]interface{})
		for k, val := range v {
			// 检查键名是否匹配
			keyMatched, _ := matchPattern(k, op.Pattern, op.MatchType, op.CaseSensitive)
			if keyMatched {
				// 如果键名匹配，替换对应的值
				result[k] = op.Replacement
			} else {
				// 递归处理值
				result[k] = replaceInJSONValue(val, op)
			}
		}
		return result
	case []interface{}:
		result := make([]interface{}, len(v))
		for i, val := range v {
			result[i] = replaceInJSONValue(val, op)
		}
		return result
	default:
		return v
	}
}

// processXMLReplace 处理XML格式的替换
func processXMLReplace(content string, op BodyKeywordOperation, logger interfaces.LogService) (string, error) {
	// XML处理相对复杂，这里使用简单的字符串替换
	// 在实际应用中可以使用更复杂的XML解析和操作
	return processTextReplace(content, op, logger)
}

// processHTMLReplace 处理HTML格式的替换
func processHTMLReplace(content string, op BodyKeywordOperation, logger interfaces.LogService) (string, error) {
	// HTML处理，注意不要破坏标签结构
	return processTextReplace(content, op, logger)
}

// processTextReplace 处理纯文本替换
func processTextReplace(content string, op BodyKeywordOperation, logger interfaces.LogService) (string, error) {
	var result string

	switch op.MatchType {
	case MatchTypeRegex:
		re, err := regexp.Compile(op.Pattern)
		if err != nil {
			return content, errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "正则表达式编译失败")
		}
		result = re.ReplaceAllString(content, op.Replacement)
	case MatchTypeExact, MatchTypeContains:
		if op.CaseSensitive {
			result = strings.ReplaceAll(content, op.Pattern, op.Replacement)
		} else {
			// 大小写不敏感替换
			lowerPattern := strings.ToLower(op.Pattern)

			// 找到所有匹配位置并替换
			result = content
			for {
				index := strings.Index(strings.ToLower(result), lowerPattern)
				if index == -1 {
					break
				}
				result = result[:index] + op.Replacement + result[index+len(op.Pattern):]
			}
		}
	default:
		result = strings.ReplaceAll(content, op.Pattern, op.Replacement)
	}

	logger.Debug("文本替换完成: 模式=%s, 替换=%s", op.Pattern, op.Replacement)
	return result, nil
}

// processBodyRemove 处理Body删除操作
func processBodyRemove(content string, op BodyKeywordOperation, logger interfaces.LogService) (string, error) {
	// 删除操作就是替换为空字符串
	removeOp := op
	removeOp.Replacement = ""
	return processBodyReplace(content, removeOp, logger)
}

// processBodyAdd 处理Body添加操作
func processBodyAdd(content string, op BodyKeywordOperation, logger interfaces.LogService) (string, error) {
	// 添加操作在内容开头添加
	result := op.Replacement + content
	logger.Debug("Body添加操作完成")
	return result, nil
}

// processBodyAppend 处理Body追加操作
func processBodyAppend(content string, op BodyKeywordOperation, logger interfaces.LogService) (string, error) {
	// 追加操作在内容末尾添加
	result := content + op.Replacement
	logger.Debug("Body追加操作完成")
	return result, nil
}

// processBodyConfig 处理body配置，返回处理后的内容和Content-Type
func processBodyConfig(bodyConfig *BodyConfig) ([]byte, string, error) {
	if bodyConfig == nil || bodyConfig.Content == "" {
		return nil, "", nil
	}

	content := bodyConfig.Content
	contentType := bodyConfig.ContentType
	format := bodyConfig.Format
	encoding := bodyConfig.Encoding

	// 处理编码
	var bodyBytes []byte
	var err error

	switch strings.ToLower(encoding) {
	case "base64":
		bodyBytes, err = base64.StdEncoding.DecodeString(content)
		if err != nil {
			return nil, "", errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "base64解码失败")
		}
	case "utf-8", "":
		bodyBytes = []byte(content)
	default:
		return nil, "", errors.NewError(errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "不支持的编码格式: "+encoding)
	}

	// 确定Content-Type
	if contentType == "" {
		if format != "" {
			contentType = getContentTypeByFormat(format)
		} else {
			contentType = detectContentType(content)
		}
	}

	// 验证内容格式（仅对文本内容）
	if encoding != "base64" && format != "" {
		if err := validateContent(content, format); err != nil {
			return nil, "", err
		}
	}

	return bodyBytes, contentType, nil
}

// replaceRequestBody 替换请求体内容（向后兼容的简单版本）
func replaceRequestBody(req *http.Request, newBody string) error {
	if newBody == "" {
		return nil
	}

	bodyBytes := []byte(newBody)
	req.Body = io.NopCloser(bytes.NewReader(bodyBytes))
	req.ContentLength = int64(len(bodyBytes))

	// 更新Content-Length头部
	req.Header.Set("Content-Length", strconv.Itoa(len(bodyBytes)))

	return nil
}

// replaceRequestBodyAdvanced 高级请求体替换，支持BodyConfig
func replaceRequestBodyAdvanced(req *http.Request, bodyConfig *BodyConfig, autoSetContentType bool) error {
	bodyBytes, contentType, err := processBodyConfig(bodyConfig)
	if err != nil {
		return err
	}

	if bodyBytes == nil {
		return nil
	}

	req.Body = io.NopCloser(bytes.NewReader(bodyBytes))
	req.ContentLength = int64(len(bodyBytes))

	// 更新Content-Length头部
	req.Header.Set("Content-Length", strconv.Itoa(len(bodyBytes)))

	// 自动设置Content-Type
	if autoSetContentType && contentType != "" {
		req.Header.Set("Content-Type", contentType)
	}

	return nil
}

// replaceResponseBody 替换响应体内容（向后兼容的简单版本）
func replaceResponseBody(resp *http.Response, newBody string) error {
	if newBody == "" {
		return nil
	}

	bodyBytes := []byte(newBody)
	resp.Body = io.NopCloser(bytes.NewReader(bodyBytes))
	resp.ContentLength = int64(len(bodyBytes))

	// 更新Content-Length头部
	resp.Header.Set("Content-Length", strconv.Itoa(len(bodyBytes)))

	return nil
}

// replaceResponseBodyAdvanced 高级响应体替换，支持BodyConfig
func replaceResponseBodyAdvanced(resp *http.Response, bodyConfig *BodyConfig, autoSetContentType bool) error {
	bodyBytes, contentType, err := processBodyConfig(bodyConfig)
	if err != nil {
		return err
	}

	if bodyBytes == nil {
		return nil
	}

	resp.Body = io.NopCloser(bytes.NewReader(bodyBytes))
	resp.ContentLength = int64(len(bodyBytes))

	// 更新Content-Length头部
	resp.Header.Set("Content-Length", strconv.Itoa(len(bodyBytes)))

	// 自动设置Content-Type
	if autoSetContentType && contentType != "" {
		resp.Header.Set("Content-Type", contentType)
	}

	return nil
}

// =============================================================================
// 内置执行器实现
// =============================================================================

// LogExecutor 日志执行器
type LogExecutor struct {
	Logger interfaces.LogService
}

func (e *LogExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	message, ok := parameters["message"].(string)
	if !ok {
		return errors.NewError(errors.ErrTypeAction, errors.ErrCodeMissingParameter, "缺少message参数")
	}

	level, _ := parameters["level"].(string)
	if level == "" {
		level = "info"
	}

	switch strings.ToLower(level) {
	case "debug":
		e.Logger.Debug(message)
	case "info":
		e.Logger.Info(message)
	case "warn":
		e.Logger.Warn(message)
	case "error":
		e.Logger.Error(message)
	default:
		e.Logger.Info(message)
	}

	return nil
}

func (e *LogExecutor) Validate(parameters map[string]interface{}) error {
	if _, ok := parameters["message"]; !ok {
		return errors.NewError(errors.ErrTypeAction, errors.ErrCodeMissingParameter, "缺少必需的message参数")
	}
	return nil
}

func (e *LogExecutor) GetType() string {
	return "log"
}

func (e *LogExecutor) GetDescription() string {
	return "记录日志信息"
}

// BanIPExecutor IP封禁执行器
type BanIPExecutor struct {
	Logger       interfaces.LogService
	ProxyService interfaces.ProxyService
}

func (e *BanIPExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	duration := "1h"
	if val, ok := parameters["duration"].(string); ok {
		duration = val
	}

	e.Logger.Info("IP封禁动作已执行: duration=%s", duration)
	return nil
}

func (e *BanIPExecutor) Validate(parameters map[string]interface{}) error {
	// duration 参数是可选的，有默认值
	return nil
}

func (e *BanIPExecutor) GetType() string {
	return "banip"
}

func (e *BanIPExecutor) GetDescription() string {
	return "封禁IP地址"
}

// BanDomainExecutor 域名封禁执行器
type BanDomainExecutor struct {
	Logger       interfaces.LogService
	ProxyService interfaces.ProxyService
}

func (e *BanDomainExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	duration := "1h"
	if val, ok := parameters["duration"].(string); ok {
		duration = val
	}

	scope := "domain"
	if val, ok := parameters["scope"].(string); ok {
		scope = val
	}

	permanent := false
	if val, ok := parameters["permanent"].(bool); ok {
		permanent = val
	}

	e.Logger.Info("域名封禁动作已执行: duration=%s, scope=%s, permanent=%v", duration, scope, permanent)
	return nil
}

func (e *BanDomainExecutor) Validate(parameters map[string]interface{}) error {
	// 所有参数都是可选的，有默认值
	return nil
}

func (e *BanDomainExecutor) GetType() string {
	return "ban_domain"
}

func (e *BanDomainExecutor) GetDescription() string {
	return "封禁域名"
}

// BlockRequestExecutor 阻止请求执行器
type BlockRequestExecutor struct {
	Logger interfaces.LogService
}

func (e *BlockRequestExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	reason, _ := parameters["reason"].(string)
	if reason == "" {
		reason = "请求被阻止"
	}

	statusCode := http.StatusForbidden
	if val, ok := parameters["status_code"]; ok {
		if sc, ok := val.(int); ok {
			statusCode = sc
		}
	}

	e.Logger.Info("阻止请求动作已执行: reason=%s, status_code=%d", reason, statusCode)
	return nil
}

func (e *BlockRequestExecutor) Validate(parameters map[string]interface{}) error {
	// 所有参数都是可选的，有默认值
	return nil
}

func (e *BlockRequestExecutor) GetType() string {
	return constants.ActionTypeBlockRequest
}

func (e *BlockRequestExecutor) GetDescription() string {
	return "阻止请求"
}

// ModifyRequestExecutor 修改请求执行器
type ModifyRequestExecutor struct {
	Logger interfaces.LogService
}

func (e *ModifyRequestExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	headers, _ := parameters["headers"].(map[string]interface{})
	removeHeaders, _ := parameters["remove_headers"].([]string)
	body, _ := parameters["body"].(string)

	e.Logger.Info("修改请求动作已执行: headers=%v, remove_headers=%v, body_length=%d", headers, removeHeaders, len(body))
	return nil
}

// RequestModificationState 请求修改状态跟踪
type RequestModificationState struct {
	OriginalHeaders    http.Header
	OriginalBody       []byte
	OriginalContentLength int64
	ModifiedSteps      []string
	FailedStep         string
	ErrorContext       map[string]interface{}
}

// ExecuteHTTP 实现HTTPExecutor接口，实际修改HTTP请求
func (e *ModifyRequestExecutor) ExecuteHTTP(ctx context.Context, parameters map[string]interface{}, req *http.Request, resp *http.Response) (*http.Request, *http.Response, error) {
	if req == nil {
		return req, resp, errors.NewError(errors.ErrTypeAction, errors.ErrCodeMissingParameter, "HTTP请求对象为空")
	}

	// 创建修改状态跟踪
	state := &RequestModificationState{
		OriginalHeaders:    make(http.Header),
		ModifiedSteps:      make([]string, 0),
		ErrorContext:       make(map[string]interface{}),
	}

	// 备份原始状态
	for k, v := range req.Header {
		state.OriginalHeaders[k] = append([]string(nil), v...)
	}
	state.OriginalContentLength = req.ContentLength

	// 备份原始body
	if req.Body != nil {
		bodyBytes, err := io.ReadAll(req.Body)
		if err != nil {
			e.Logger.Error("读取原始请求体失败: %v", err)
			return req, resp, errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "读取原始请求体失败")
		}
		req.Body.Close()
		state.OriginalBody = bodyBytes
		req.Body = io.NopCloser(bytes.NewReader(bodyBytes))
	}

	// 解析参数
	headers, _ := parameters["headers"].(map[string]interface{})
	removeHeaders, _ := parameters["remove_headers"].([]string)
	body, _ := parameters["body"].(string)
	bodyConfigRaw, _ := parameters["body_config"]
	keywordOperationsRaw, _ := parameters["keyword_operations"]
	autoSetContentType, _ := parameters["auto_content_type"].(bool)

	// 默认启用自动Content-Type设置
	if _, exists := parameters["auto_content_type"]; !exists {
		autoSetContentType = true
	}

	e.Logger.Info("开始修改HTTP请求: headers=%v, remove_headers=%v, body_length=%d, has_body_config=%v, has_keyword_operations=%v",
		headers, removeHeaders, len(body), bodyConfigRaw != nil, keywordOperationsRaw != nil)

	// 使用defer确保在发生错误时能够回退
	defer func() {
		if r := recover(); r != nil {
			e.Logger.Error("请求修改过程中发生panic: %v", r)
			e.rollbackRequestModification(req, state)
		}
	}()

	// 1. 处理关键字操作（优先级最高）
	if keywordOperationsRaw != nil {
		state.ErrorContext["step"] = "keyword_operations"
		state.ErrorContext["operations"] = keywordOperationsRaw

		err := e.processRequestKeywordOperations(req, keywordOperationsRaw)
		if err != nil {
			state.FailedStep = "keyword_operations"
			e.Logger.Error("关键字操作失败: %v", err)
			e.rollbackRequestModification(req, state)
			return req, resp, errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed,
				fmt.Sprintf("关键字操作失败，已回退所有修改。错误详情: %v", err))
		}
		state.ModifiedSteps = append(state.ModifiedSteps, "keyword_operations")
		e.Logger.Debug("关键字操作完成")
	}

	// 2. 处理传统的headers修改
	if headers != nil || removeHeaders != nil {
		state.ErrorContext["step"] = "headers"
		state.ErrorContext["headers"] = headers
		state.ErrorContext["remove_headers"] = removeHeaders

		err := e.safeModifyHTTPHeaders(req.Header, headers, removeHeaders)
		if err != nil {
			state.FailedStep = "headers"
			e.Logger.Error("请求头修改失败: %v", err)
			e.rollbackRequestModification(req, state)
			return req, resp, errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed,
				fmt.Sprintf("请求头修改失败，已回退所有修改。错误详情: %v", err))
		}
		state.ModifiedSteps = append(state.ModifiedSteps, "headers")
		e.Logger.Debug("请求头修改完成")
	}

	// 3. 处理body修改 - 优先使用body_config，然后是简单的body参数
	if bodyConfigRaw != nil {
		state.ErrorContext["step"] = "body_config"
		state.ErrorContext["body_config"] = bodyConfigRaw

		// 解析body_config
		var bodyConfig BodyConfig
		if configMap, ok := bodyConfigRaw.(map[string]interface{}); ok {
			if content, ok := configMap["content"].(string); ok {
				bodyConfig.Content = content
			}
			if contentType, ok := configMap["content_type"].(string); ok {
				bodyConfig.ContentType = contentType
			}
			if format, ok := configMap["format"].(string); ok {
				bodyConfig.Format = format
			}
			if encoding, ok := configMap["encoding"].(string); ok {
				bodyConfig.Encoding = encoding
			}
		}

		err := replaceRequestBodyAdvanced(req, &bodyConfig, autoSetContentType)
		if err != nil {
			state.FailedStep = "body_config"
			e.Logger.Error("高级请求体修改失败: %v", err)
			e.rollbackRequestModification(req, state)
			return req, resp, errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed,
				fmt.Sprintf("高级请求体修改失败，已回退所有修改。错误详情: %v", err))
		}
		state.ModifiedSteps = append(state.ModifiedSteps, "body_config")
		e.Logger.Debug("高级请求体修改完成，内容长度: %d", req.ContentLength)
	} else if body != "" {
		state.ErrorContext["step"] = "body"
		state.ErrorContext["body_length"] = len(body)

		// 使用简单的body参数（向后兼容）
		err := replaceRequestBody(req, body)
		if err != nil {
			state.FailedStep = "body"
			e.Logger.Error("请求体修改失败: %v", err)
			e.rollbackRequestModification(req, state)
			return req, resp, errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed,
				fmt.Sprintf("请求体修改失败，已回退所有修改。错误详情: %v", err))
		}
		state.ModifiedSteps = append(state.ModifiedSteps, "body")
		e.Logger.Debug("请求体修改完成，新长度: %d", len(body))
	}

	e.Logger.Info("HTTP请求修改完成，成功执行步骤: %v", state.ModifiedSteps)
	return req, resp, nil
}

// rollbackRequestModification 回退请求修改
func (e *ModifyRequestExecutor) rollbackRequestModification(req *http.Request, state *RequestModificationState) {
	e.Logger.Warn("开始回退请求修改，失败步骤: %s，已完成步骤: %v", state.FailedStep, state.ModifiedSteps)

	// 恢复原始headers
	req.Header = make(http.Header)
	for k, v := range state.OriginalHeaders {
		req.Header[k] = append([]string(nil), v...)
	}

	// 恢复原始body
	if state.OriginalBody != nil {
		req.Body = io.NopCloser(bytes.NewReader(state.OriginalBody))
		req.ContentLength = state.OriginalContentLength
	} else {
		req.Body = nil
		req.ContentLength = 0
	}

	e.Logger.Info("请求修改回退完成")
}

// safeModifyHTTPHeaders 安全的HTTP头部修改
func (e *ModifyRequestExecutor) safeModifyHTTPHeaders(header http.Header, headers map[string]interface{}, removeHeaders []string) error {
	// 验证headers参数
	if headers != nil {
		for key, value := range headers {
			if key == "" {
				return errors.NewError(errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "请求头名称不能为空")
			}
			if valueStr, ok := value.(string); ok {
				if len(valueStr) > 8192 { // 限制头部值的长度
					return errors.NewError(errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed,
						fmt.Sprintf("请求头 %s 的值过长，最大允许8192字符", key))
				}
			}
		}
	}

	// 验证removeHeaders参数
	if removeHeaders != nil {
		for _, headerName := range removeHeaders {
			if headerName == "" {
				return errors.NewError(errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "要删除的请求头名称不能为空")
			}
		}
	}

	// 执行修改
	modifyHTTPHeaders(header, headers, removeHeaders)
	return nil
}

// processRequestKeywordOperations 处理请求的关键字操作
func (e *ModifyRequestExecutor) processRequestKeywordOperations(req *http.Request, keywordOperationsRaw interface{}) error {
	// 解析keyword_operations
	var keywordOps KeywordOperations
	if opsMap, ok := keywordOperationsRaw.(map[string]interface{}); ok {
		// 解析headers操作
		if headersRaw, exists := opsMap["headers"]; exists {
			if headersList, ok := headersRaw.([]interface{}); ok {
				for _, headerOpRaw := range headersList {
					if headerOpMap, ok := headerOpRaw.(map[string]interface{}); ok {
						headerOp := HeaderKeywordOperation{}
						if operation, ok := headerOpMap["operation"].(string); ok {
							headerOp.Operation = OperationType(operation)
						}
						if matchType, ok := headerOpMap["match_type"].(string); ok {
							headerOp.MatchType = MatchType(matchType)
						}
						if pattern, ok := headerOpMap["pattern"].(string); ok {
							headerOp.Pattern = pattern
						}
						if valuePattern, ok := headerOpMap["value_pattern"].(string); ok {
							headerOp.ValuePattern = valuePattern
						}
						if replacement, ok := headerOpMap["replacement"].(string); ok {
							headerOp.Replacement = replacement
						}
						if newValue, ok := headerOpMap["new_value"].(string); ok {
							headerOp.NewValue = newValue
						}
						if condition, ok := headerOpMap["condition"].(string); ok {
							headerOp.Condition = ConditionType(condition)
						}
						if caseSensitive, ok := headerOpMap["case_sensitive"].(bool); ok {
							headerOp.CaseSensitive = caseSensitive
						}
						keywordOps.Headers = append(keywordOps.Headers, headerOp)
					}
				}
			}
		}

		// 解析body操作
		if bodyRaw, exists := opsMap["body"]; exists {
			if bodyList, ok := bodyRaw.([]interface{}); ok {
				for _, bodyOpRaw := range bodyList {
					if bodyOpMap, ok := bodyOpRaw.(map[string]interface{}); ok {
						bodyOp := BodyKeywordOperation{}
						if operation, ok := bodyOpMap["operation"].(string); ok {
							bodyOp.Operation = OperationType(operation)
						}
						if format, ok := bodyOpMap["format"].(string); ok {
							bodyOp.Format = format
						}
						if matchType, ok := bodyOpMap["match_type"].(string); ok {
							bodyOp.MatchType = MatchType(matchType)
						}
						if pattern, ok := bodyOpMap["pattern"].(string); ok {
							bodyOp.Pattern = pattern
						}
						if replacement, ok := bodyOpMap["replacement"].(string); ok {
							bodyOp.Replacement = replacement
						}
						if caseSensitive, ok := bodyOpMap["case_sensitive"].(bool); ok {
							bodyOp.CaseSensitive = caseSensitive
						}
						if preserveStructure, ok := bodyOpMap["preserve_structure"].(bool); ok {
							bodyOp.PreserveStructure = preserveStructure
						}
						keywordOps.Body = append(keywordOps.Body, bodyOp)
					}
				}
			}
		}
	}

	// 执行Header关键字操作
	if len(keywordOps.Headers) > 0 {
		err := processHeaderKeywordOperations(req.Header, keywordOps.Headers, e.Logger)
		if err != nil {
			return err
		}
	}

	// 执行Body关键字操作
	if len(keywordOps.Body) > 0 && req.Body != nil {
		// 读取原始body内容
		bodyBytes, err := io.ReadAll(req.Body)
		if err != nil {
			return errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "读取请求体失败")
		}
		req.Body.Close()

		// 执行关键字操作
		originalContent := string(bodyBytes)
		modifiedContent, err := processBodyKeywordOperations(originalContent, keywordOps.Body, e.Logger)
		if err != nil {
			return err
		}

		// 更新请求体
		req.Body = io.NopCloser(strings.NewReader(modifiedContent))
		req.ContentLength = int64(len(modifiedContent))
		req.Header.Set("Content-Length", strconv.Itoa(len(modifiedContent)))
	}

	return nil
}

func (e *ModifyRequestExecutor) Validate(parameters map[string]interface{}) error {
	// 验证body_config参数
	if bodyConfigRaw, exists := parameters["body_config"]; exists {
		if err := validateBodyConfig(bodyConfigRaw, "modify_request"); err != nil {
			return err
		}
	}

	// 验证headers参数
	if headersRaw, exists := parameters["headers"]; exists {
		if _, ok := headersRaw.(map[string]interface{}); !ok {
			return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				"modify_request动作的headers参数必须是键值对格式")
		}
	}

	// 验证remove_headers参数
	if removeHeadersRaw, exists := parameters["remove_headers"]; exists {
		if removeHeaders, ok := removeHeadersRaw.([]interface{}); ok {
			for i, header := range removeHeaders {
				if _, ok := header.(string); !ok {
					return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
						fmt.Sprintf("modify_request动作的remove_headers[%d]必须是字符串", i))
				}
			}
		} else if _, ok := removeHeadersRaw.([]string); !ok {
			return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				"modify_request动作的remove_headers参数必须是字符串数组")
		}
	}

	// 验证keyword_operations参数
	if keywordOpsRaw, exists := parameters["keyword_operations"]; exists {
		if err := validateKeywordOperations(keywordOpsRaw, "modify_request"); err != nil {
			return err
		}
	}

	return nil
}

func (e *ModifyRequestExecutor) GetType() string {
	return "modify_request"
}

func (e *ModifyRequestExecutor) GetDescription() string {
	return "修改请求内容"
}

// ModifyResponseExecutor 修改响应执行器
type ModifyResponseExecutor struct {
	Logger interfaces.LogService
}

func (e *ModifyResponseExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	headers, _ := parameters["headers"].(map[string]interface{})
	removeHeaders, _ := parameters["remove_headers"].([]string)
	statusCode, _ := parameters["status_code"].(int)
	body, _ := parameters["body"].(string)

	e.Logger.Info("修改响应动作已执行: headers=%v, remove_headers=%v, status_code=%d, body_length=%d", headers, removeHeaders, statusCode, len(body))
	return nil
}

// ResponseModificationState 响应修改状态跟踪
type ResponseModificationState struct {
	OriginalHeaders       http.Header
	OriginalBody          []byte
	OriginalContentLength int64
	OriginalStatusCode    int
	OriginalStatus        string
	ModifiedSteps         []string
	FailedStep            string
	ErrorContext          map[string]interface{}
}

// ExecuteHTTP 实现HTTPExecutor接口，实际修改HTTP响应
func (e *ModifyResponseExecutor) ExecuteHTTP(ctx context.Context, parameters map[string]interface{}, req *http.Request, resp *http.Response) (*http.Request, *http.Response, error) {
	if resp == nil {
		return req, resp, errors.NewError(errors.ErrTypeAction, errors.ErrCodeMissingParameter, "HTTP响应对象为空")
	}

	// 创建修改状态跟踪
	state := &ResponseModificationState{
		OriginalHeaders:       make(http.Header),
		ModifiedSteps:         make([]string, 0),
		ErrorContext:          make(map[string]interface{}),
		OriginalStatusCode:    resp.StatusCode,
		OriginalStatus:        resp.Status,
		OriginalContentLength: resp.ContentLength,
	}

	// 备份原始状态
	for k, v := range resp.Header {
		state.OriginalHeaders[k] = append([]string(nil), v...)
	}

	// 备份原始body
	if resp.Body != nil {
		bodyBytes, err := io.ReadAll(resp.Body)
		if err != nil {
			e.Logger.Error("读取原始响应体失败: %v", err)
			return req, resp, errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "读取原始响应体失败")
		}
		resp.Body.Close()
		state.OriginalBody = bodyBytes
		resp.Body = io.NopCloser(bytes.NewReader(bodyBytes))
	}

	// 解析参数
	headers, _ := parameters["headers"].(map[string]interface{})
	removeHeaders, _ := parameters["remove_headers"].([]string)
	statusCode, _ := parameters["status_code"].(int)
	body, _ := parameters["body"].(string)
	bodyConfigRaw, _ := parameters["body_config"]
	keywordOperationsRaw, _ := parameters["keyword_operations"]
	autoSetContentType, _ := parameters["auto_content_type"].(bool)

	// 默认启用自动Content-Type设置
	if _, exists := parameters["auto_content_type"]; !exists {
		autoSetContentType = true
	}

	e.Logger.Info("开始修改HTTP响应: headers=%v, remove_headers=%v, status_code=%d, body_length=%d, has_body_config=%v, has_keyword_operations=%v",
		headers, removeHeaders, statusCode, len(body), bodyConfigRaw != nil, keywordOperationsRaw != nil)

	// 使用defer确保在发生错误时能够回退
	defer func() {
		if r := recover(); r != nil {
			e.Logger.Error("响应修改过程中发生panic: %v", r)
			e.rollbackResponseModification(resp, state)
		}
	}()

	// 1. 修改响应状态码
	if statusCode > 0 {
		state.ErrorContext["step"] = "status_code"
		state.ErrorContext["status_code"] = statusCode

		if statusCode < 100 || statusCode > 599 {
			state.FailedStep = "status_code"
			e.Logger.Error("无效的状态码: %d", statusCode)
			e.rollbackResponseModification(resp, state)
			return req, resp, errors.NewError(errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed,
				fmt.Sprintf("无效的状态码: %d，状态码必须在100-599范围内", statusCode))
		}

		resp.StatusCode = statusCode
		resp.Status = http.StatusText(statusCode)
		state.ModifiedSteps = append(state.ModifiedSteps, "status_code")
		e.Logger.Debug("响应状态码修改为: %d", statusCode)
	}

	// 2. 处理关键字操作（优先级高）
	if keywordOperationsRaw != nil {
		state.ErrorContext["step"] = "keyword_operations"
		state.ErrorContext["operations"] = keywordOperationsRaw

		err := e.processResponseKeywordOperations(resp, keywordOperationsRaw)
		if err != nil {
			state.FailedStep = "keyword_operations"
			e.Logger.Error("关键字操作失败: %v", err)
			e.rollbackResponseModification(resp, state)
			return req, resp, errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed,
				fmt.Sprintf("关键字操作失败，已回退所有修改。错误详情: %v", err))
		}
		state.ModifiedSteps = append(state.ModifiedSteps, "keyword_operations")
		e.Logger.Debug("关键字操作完成")
	}

	// 3. 处理传统的headers修改
	if headers != nil || removeHeaders != nil {
		state.ErrorContext["step"] = "headers"
		state.ErrorContext["headers"] = headers
		state.ErrorContext["remove_headers"] = removeHeaders

		err := e.safeModifyHTTPHeaders(resp.Header, headers, removeHeaders)
		if err != nil {
			state.FailedStep = "headers"
			e.Logger.Error("响应头修改失败: %v", err)
			e.rollbackResponseModification(resp, state)
			return req, resp, errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed,
				fmt.Sprintf("响应头修改失败，已回退所有修改。错误详情: %v", err))
		}
		state.ModifiedSteps = append(state.ModifiedSteps, "headers")
		e.Logger.Debug("响应头修改完成")
	}

	// 4. 处理body修改 - 优先使用body_config，然后是简单的body参数
	if bodyConfigRaw != nil {
		state.ErrorContext["step"] = "body_config"
		state.ErrorContext["body_config"] = bodyConfigRaw

		// 解析body_config
		var bodyConfig BodyConfig
		if configMap, ok := bodyConfigRaw.(map[string]interface{}); ok {
			if content, ok := configMap["content"].(string); ok {
				bodyConfig.Content = content
			}
			if contentType, ok := configMap["content_type"].(string); ok {
				bodyConfig.ContentType = contentType
			}
			if format, ok := configMap["format"].(string); ok {
				bodyConfig.Format = format
			}
			if encoding, ok := configMap["encoding"].(string); ok {
				bodyConfig.Encoding = encoding
			}
		}

		err := replaceResponseBodyAdvanced(resp, &bodyConfig, autoSetContentType)
		if err != nil {
			state.FailedStep = "body_config"
			e.Logger.Error("高级响应体修改失败: %v", err)
			e.rollbackResponseModification(resp, state)
			return req, resp, errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed,
				fmt.Sprintf("高级响应体修改失败，已回退所有修改。错误详情: %v", err))
		}
		state.ModifiedSteps = append(state.ModifiedSteps, "body_config")
		e.Logger.Debug("高级响应体修改完成，内容长度: %d", resp.ContentLength)
	} else if body != "" {
		state.ErrorContext["step"] = "body"
		state.ErrorContext["body_length"] = len(body)

		// 使用简单的body参数（向后兼容）
		err := replaceResponseBody(resp, body)
		if err != nil {
			state.FailedStep = "body"
			e.Logger.Error("响应体修改失败: %v", err)
			e.rollbackResponseModification(resp, state)
			return req, resp, errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed,
				fmt.Sprintf("响应体修改失败，已回退所有修改。错误详情: %v", err))
		}
		state.ModifiedSteps = append(state.ModifiedSteps, "body")
		e.Logger.Debug("响应体修改完成，新长度: %d", len(body))
	}

	e.Logger.Info("HTTP响应修改完成，成功执行步骤: %v", state.ModifiedSteps)
	return req, resp, nil
}

// rollbackResponseModification 回退响应修改
func (e *ModifyResponseExecutor) rollbackResponseModification(resp *http.Response, state *ResponseModificationState) {
	e.Logger.Warn("开始回退响应修改，失败步骤: %s，已完成步骤: %v", state.FailedStep, state.ModifiedSteps)

	// 恢复原始headers
	resp.Header = make(http.Header)
	for k, v := range state.OriginalHeaders {
		resp.Header[k] = append([]string(nil), v...)
	}

	// 恢复原始状态码
	resp.StatusCode = state.OriginalStatusCode
	resp.Status = state.OriginalStatus

	// 恢复原始body
	if state.OriginalBody != nil {
		resp.Body = io.NopCloser(bytes.NewReader(state.OriginalBody))
		resp.ContentLength = state.OriginalContentLength
	} else {
		resp.Body = nil
		resp.ContentLength = 0
	}

	e.Logger.Info("响应修改回退完成")
}

// safeModifyHTTPHeaders 安全的HTTP头部修改（响应版本）
func (e *ModifyResponseExecutor) safeModifyHTTPHeaders(header http.Header, headers map[string]interface{}, removeHeaders []string) error {
	// 验证headers参数
	if headers != nil {
		for key, value := range headers {
			if key == "" {
				return errors.NewError(errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "响应头名称不能为空")
			}
			if valueStr, ok := value.(string); ok {
				if len(valueStr) > 8192 { // 限制头部值的长度
					return errors.NewError(errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed,
						fmt.Sprintf("响应头 %s 的值过长，最大允许8192字符", key))
				}
			}
		}
	}

	// 验证removeHeaders参数
	if removeHeaders != nil {
		for _, headerName := range removeHeaders {
			if headerName == "" {
				return errors.NewError(errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "要删除的响应头名称不能为空")
			}
		}
	}

	// 执行修改
	modifyHTTPHeaders(header, headers, removeHeaders)
	return nil
}

// processResponseKeywordOperations 处理响应的关键字操作
func (e *ModifyResponseExecutor) processResponseKeywordOperations(resp *http.Response, keywordOperationsRaw interface{}) error {
	// 解析keyword_operations（与请求处理类似）
	var keywordOps KeywordOperations
	if opsMap, ok := keywordOperationsRaw.(map[string]interface{}); ok {
		// 解析headers操作
		if headersRaw, exists := opsMap["headers"]; exists {
			if headersList, ok := headersRaw.([]interface{}); ok {
				for _, headerOpRaw := range headersList {
					if headerOpMap, ok := headerOpRaw.(map[string]interface{}); ok {
						headerOp := HeaderKeywordOperation{}
						if operation, ok := headerOpMap["operation"].(string); ok {
							headerOp.Operation = OperationType(operation)
						}
						if matchType, ok := headerOpMap["match_type"].(string); ok {
							headerOp.MatchType = MatchType(matchType)
						}
						if pattern, ok := headerOpMap["pattern"].(string); ok {
							headerOp.Pattern = pattern
						}
						if valuePattern, ok := headerOpMap["value_pattern"].(string); ok {
							headerOp.ValuePattern = valuePattern
						}
						if replacement, ok := headerOpMap["replacement"].(string); ok {
							headerOp.Replacement = replacement
						}
						if newValue, ok := headerOpMap["new_value"].(string); ok {
							headerOp.NewValue = newValue
						}
						if condition, ok := headerOpMap["condition"].(string); ok {
							headerOp.Condition = ConditionType(condition)
						}
						if caseSensitive, ok := headerOpMap["case_sensitive"].(bool); ok {
							headerOp.CaseSensitive = caseSensitive
						}
						keywordOps.Headers = append(keywordOps.Headers, headerOp)
					}
				}
			}
		}

		// 解析body操作
		if bodyRaw, exists := opsMap["body"]; exists {
			if bodyList, ok := bodyRaw.([]interface{}); ok {
				for _, bodyOpRaw := range bodyList {
					if bodyOpMap, ok := bodyOpRaw.(map[string]interface{}); ok {
						bodyOp := BodyKeywordOperation{}
						if operation, ok := bodyOpMap["operation"].(string); ok {
							bodyOp.Operation = OperationType(operation)
						}
						if format, ok := bodyOpMap["format"].(string); ok {
							bodyOp.Format = format
						}
						if matchType, ok := bodyOpMap["match_type"].(string); ok {
							bodyOp.MatchType = MatchType(matchType)
						}
						if pattern, ok := bodyOpMap["pattern"].(string); ok {
							bodyOp.Pattern = pattern
						}
						if replacement, ok := bodyOpMap["replacement"].(string); ok {
							bodyOp.Replacement = replacement
						}
						if caseSensitive, ok := bodyOpMap["case_sensitive"].(bool); ok {
							bodyOp.CaseSensitive = caseSensitive
						}
						if preserveStructure, ok := bodyOpMap["preserve_structure"].(bool); ok {
							bodyOp.PreserveStructure = preserveStructure
						}
						keywordOps.Body = append(keywordOps.Body, bodyOp)
					}
				}
			}
		}
	}

	// 执行Header关键字操作
	if len(keywordOps.Headers) > 0 {
		err := processHeaderKeywordOperations(resp.Header, keywordOps.Headers, e.Logger)
		if err != nil {
			return err
		}
	}

	// 执行Body关键字操作
	if len(keywordOps.Body) > 0 && resp.Body != nil {
		// 读取原始body内容
		bodyBytes, err := io.ReadAll(resp.Body)
		if err != nil {
			return errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "读取响应体失败")
		}
		resp.Body.Close()

		// 执行关键字操作
		originalContent := string(bodyBytes)
		modifiedContent, err := processBodyKeywordOperations(originalContent, keywordOps.Body, e.Logger)
		if err != nil {
			return err
		}

		// 更新响应体
		resp.Body = io.NopCloser(strings.NewReader(modifiedContent))
		resp.ContentLength = int64(len(modifiedContent))
		resp.Header.Set("Content-Length", strconv.Itoa(len(modifiedContent)))
	}

	return nil
}

func (e *ModifyResponseExecutor) Validate(parameters map[string]interface{}) error {
	// 验证body_config参数
	if bodyConfigRaw, exists := parameters["body_config"]; exists {
		if err := validateBodyConfig(bodyConfigRaw, "modify_response"); err != nil {
			return err
		}
	}

	// 验证headers参数
	if headersRaw, exists := parameters["headers"]; exists {
		if _, ok := headersRaw.(map[string]interface{}); !ok {
			return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				"modify_response动作的headers参数必须是键值对格式")
		}
	}

	// 验证remove_headers参数
	if removeHeadersRaw, exists := parameters["remove_headers"]; exists {
		if removeHeaders, ok := removeHeadersRaw.([]interface{}); ok {
			for i, header := range removeHeaders {
				if _, ok := header.(string); !ok {
					return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
						fmt.Sprintf("modify_response动作的remove_headers[%d]必须是字符串", i))
				}
			}
		} else if _, ok := removeHeadersRaw.([]string); !ok {
			return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				"modify_response动作的remove_headers参数必须是字符串数组")
		}
	}

	// 验证status_code参数
	if statusCodeRaw, exists := parameters["status_code"]; exists {
		if statusCode, ok := statusCodeRaw.(int); ok {
			if statusCode < 100 || statusCode > 599 {
				return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
					fmt.Sprintf("modify_response动作的status_code必须在100-599范围内，当前值: %d", statusCode))
			}
		} else {
			return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				"modify_response动作的status_code参数必须是整数")
		}
	}

	// 验证keyword_operations参数
	if keywordOpsRaw, exists := parameters["keyword_operations"]; exists {
		if err := validateKeywordOperations(keywordOpsRaw, "modify_response"); err != nil {
			return err
		}
	}

	return nil
}

func (e *ModifyResponseExecutor) GetType() string {
	return "modify_response"
}

func (e *ModifyResponseExecutor) GetDescription() string {
	return "修改响应内容"
}

// CacheResponseExecutor 缓存响应执行器
type CacheResponseExecutor struct {
	Logger       interfaces.LogService
	CacheService interfaces.CacheService
}

func (e *CacheResponseExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	duration := 300 // 默认5分钟
	if val, ok := parameters["duration"]; ok {
		switch v := val.(type) {
		case int:
			duration = v
		case float64:
			duration = int(v)
		case string:
			if d, err := strconv.Atoi(v); err == nil {
				duration = d
			}
		}
	}

	key, _ := parameters["key"].(string)
	if key == "" {
		key = "default"
	}

	e.Logger.Info("缓存响应动作已执行: duration=%d, key=%s", duration, key)
	return nil
}

func (e *CacheResponseExecutor) Validate(parameters map[string]interface{}) error {
	// 所有参数都是可选的，有默认值
	return nil
}

func (e *CacheResponseExecutor) GetType() string {
	return "cache_response"
}

func (e *CacheResponseExecutor) GetDescription() string {
	return "缓存响应内容"
}

// ScriptExecutor 脚本执行器
type ScriptExecutor struct {
	Logger interfaces.LogService
}

func (e *ScriptExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	script, ok := parameters["script"].(string)
	if !ok {
		return errors.NewError(errors.ErrTypeAction, errors.ErrCodeMissingParameter, "缺少script参数")
	}

	language, _ := parameters["language"].(string)
	if language == "" {
		language = "javascript"
	}

	timeout := 30
	if val, ok := parameters["timeout"]; ok {
		switch v := val.(type) {
		case int:
			timeout = v
		case float64:
			timeout = int(v)
		case string:
			if t, err := strconv.Atoi(v); err == nil {
				timeout = t
			}
		}
	}

	e.Logger.Info("脚本执行动作已执行: language=%s, timeout=%d, script_length=%d", language, timeout, len(script))
	return nil
}

func (e *ScriptExecutor) Validate(parameters map[string]interface{}) error {
	if _, ok := parameters["script"]; !ok {
		return errors.NewError(errors.ErrTypeAction, errors.ErrCodeMissingParameter, "缺少必需的script参数")
	}
	return nil
}

func (e *ScriptExecutor) GetType() string {
	return "script"
}

func (e *ScriptExecutor) GetDescription() string {
	return "执行自定义脚本"
}

// RetrySameExecutor 使用相同IP重试执行器
type RetrySameExecutor struct {
	Logger interfaces.LogService
}

func (e *RetrySameExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	retryCount := 1
	if val, ok := parameters["retry_count"]; ok {
		switch v := val.(type) {
		case int:
			retryCount = v
		case float64:
			retryCount = int(v)
		case string:
			if rc, err := strconv.Atoi(v); err == nil {
				retryCount = rc
			}
		}
	} else if val, ok := parameters["attempts"]; ok {
		switch v := val.(type) {
		case int:
			retryCount = v
		case float64:
			retryCount = int(v)
		case string:
			if rc, err := strconv.Atoi(v); err == nil {
				retryCount = rc
			}
		}
	}

	delay := "1s"
	if val, ok := parameters["delay"].(string); ok {
		delay = val
	}

	e.Logger.Info("标记使用相同IP重试 %d 次, 延迟: %s", retryCount, delay)
	return nil
}

func (e *RetrySameExecutor) Validate(parameters map[string]interface{}) error {
	// 所有参数都是可选的，有默认值
	return nil
}

func (e *RetrySameExecutor) GetType() string {
	return "retry_same"
}

func (e *RetrySameExecutor) GetDescription() string {
	return "使用相同IP重试请求"
}

// RetryExecutor 使用新IP重试执行器
type RetryExecutor struct {
	Logger interfaces.LogService
}

func (e *RetryExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	retryCount := 1
	if val, ok := parameters["retry_count"]; ok {
		switch v := val.(type) {
		case int:
			retryCount = v
		case float64:
			retryCount = int(v)
		case string:
			if rc, err := strconv.Atoi(v); err == nil {
				retryCount = rc
			}
		}
	} else if val, ok := parameters["attempts"]; ok {
		switch v := val.(type) {
		case int:
			retryCount = v
		case float64:
			retryCount = int(v)
		case string:
			if rc, err := strconv.Atoi(v); err == nil {
				retryCount = rc
			}
		}
	}

	delay := "2s"
	if val, ok := parameters["delay"].(string); ok {
		delay = val
	}

	e.Logger.Info("标记使用新IP重试 %d 次, 延迟: %s", retryCount, delay)
	return nil
}

func (e *RetryExecutor) Validate(parameters map[string]interface{}) error {
	// 所有参数都是可选的，有默认值
	return nil
}

func (e *RetryExecutor) GetType() string {
	return "retry"
}

func (e *RetryExecutor) GetDescription() string {
	return "使用新IP重试请求"
}

// BanIPDomainExecutor 针对域名封禁IP执行器
type BanIPDomainExecutor struct {
	Logger       interfaces.LogService
	ProxyService interfaces.ProxyService
}

func (e *BanIPDomainExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	duration := "reboot"
	if val, ok := parameters["duration"]; ok {
		switch v := val.(type) {
		case string:
			duration = v
		case int:
			duration = strconv.Itoa(v)
		case float64:
			duration = strconv.Itoa(int(v))
		}
	}

	scope := "domain"
	if val, ok := parameters["scope"].(string); ok {
		scope = val
	}

	e.Logger.Info("针对域名封禁IP动作已执行: duration=%s, scope=%s", duration, scope)
	return nil
}

func (e *BanIPDomainExecutor) Validate(parameters map[string]interface{}) error {
	// duration 和 scope 都是可选参数，有默认值
	return nil
}

func (e *BanIPDomainExecutor) GetType() string {
	return "banipdomain"
}

func (e *BanIPDomainExecutor) GetDescription() string {
	return "针对域名封禁IP"
}

// SaveToPoolExecutor 保存到代理池执行器
type SaveToPoolExecutor struct {
	Logger       interfaces.LogService
	ProxyService interfaces.ProxyService
}

func (e *SaveToPoolExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	qualityTier := "auto"
	if val, ok := parameters["quality_tier"].(string); ok {
		qualityTier = val
	}

	domainSpecific := false
	if val, ok := parameters["domain_specific"].(bool); ok {
		domainSpecific = val
	}

	minScore := 70.0
	if val, ok := parameters["min_score"]; ok {
		switch v := val.(type) {
		case float64:
			minScore = v
		case int:
			minScore = float64(v)
		case string:
			if ms, err := strconv.ParseFloat(v, 64); err == nil {
				minScore = ms
			}
		}
	}

	poolName := "default_pool"
	if val, ok := parameters["pool_name"].(string); ok {
		poolName = val
	}

	e.Logger.Info("保存到代理池动作已执行: quality_tier=%s, domain_specific=%v, min_score=%.1f, pool_name=%s", qualityTier, domainSpecific, minScore, poolName)
	return nil
}

func (e *SaveToPoolExecutor) Validate(parameters map[string]interface{}) error {
	// 所有参数都是可选的，有默认值
	return nil
}

func (e *SaveToPoolExecutor) GetType() string {
	return "save_to_pool"
}

func (e *SaveToPoolExecutor) GetDescription() string {
	return "保存代理到质量池"
}

// CacheExecutor 缓存执行器
type CacheExecutor struct {
	Logger       interfaces.LogService
	CacheService interfaces.CacheService
}

func (e *CacheExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	duration := 300000 // 默认5分钟（毫秒）
	if val, ok := parameters["duration"]; ok {
		switch v := val.(type) {
		case float64:
			duration = int(v)
		case int:
			duration = v
		case string:
			if d, err := strconv.Atoi(v); err == nil {
				duration = d
			}
		}
	}

	maxUseCount := 0
	if val, ok := parameters["max_use_count"]; ok {
		switch v := val.(type) {
		case float64:
			maxUseCount = int(v)
		case int:
			maxUseCount = v
		case string:
			if mu, err := strconv.Atoi(v); err == nil {
				maxUseCount = mu
			}
		}
	}

	cacheScope := "url"
	if val, ok := parameters["cache_scope"].(string); ok {
		cacheScope = val
	}

	customKey := ""
	if val, ok := parameters["custom_key"].(string); ok {
		customKey = val
	}

	ignoreParams := false
	if val, ok := parameters["ignore_params"].(bool); ok {
		ignoreParams = val
	}

	e.Logger.Info("缓存动作已执行: duration=%d, max_use_count=%d, cache_scope=%s, custom_key=%s, ignore_params=%v",
		duration, maxUseCount, cacheScope, customKey, ignoreParams)
	return nil
}

func (e *CacheExecutor) Validate(parameters map[string]interface{}) error {
	// 所有参数都是可选的，有默认值
	return nil
}

func (e *CacheExecutor) GetType() string {
	return "cache"
}

func (e *CacheExecutor) GetDescription() string {
	return "缓存响应内容"
}

// RequestURLExecutor 请求URL执行器
type RequestURLExecutor struct {
	Logger interfaces.LogService
}

func (e *RequestURLExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	url, ok := parameters["url"].(string)
	if !ok {
		return errors.NewError(errors.ErrTypeAction, errors.ErrCodeMissingParameter, "缺少url参数")
	}

	method := "GET"
	if val, ok := parameters["method"].(string); ok {
		method = strings.ToUpper(val)
	}

	timeoutMS := 30000 // 默认30秒
	if val, ok := parameters["timeout_ms"]; ok {
		switch v := val.(type) {
		case float64:
			timeoutMS = int(v)
		case int:
			timeoutMS = v
		case string:
			if t, err := strconv.Atoi(v); err == nil {
				timeoutMS = t
			}
		}
	}

	followRedirect := true
	if val, ok := parameters["follow_redirect"].(bool); ok {
		followRedirect = val
	}

	e.Logger.Info("请求URL动作已执行: %s %s (timeout: %dms, follow_redirect: %v)", method, url, timeoutMS, followRedirect)
	return nil
}

func (e *RequestURLExecutor) Validate(parameters map[string]interface{}) error {
	if _, ok := parameters["url"]; !ok {
		return errors.NewError(errors.ErrTypeAction, errors.ErrCodeMissingParameter, "缺少必需的url参数")
	}
	return nil
}

func (e *RequestURLExecutor) GetType() string {
	return "request_url"
}

func (e *RequestURLExecutor) GetDescription() string {
	return "向指定URL发送请求"
}

// NullResponseExecutor 空响应执行器
type NullResponseExecutor struct {
	Logger interfaces.LogService
}

func (e *NullResponseExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	statusCode := 200
	if val, ok := parameters["status_code"]; ok {
		switch v := val.(type) {
		case float64:
			statusCode = int(v)
		case int:
			statusCode = v
		case string:
			if sc, err := strconv.Atoi(v); err == nil {
				statusCode = sc
			}
		}
	}

	contentType := "text/plain"
	if val, ok := parameters["content_type"].(string); ok {
		contentType = val
	}

	body := ""
	if val, ok := parameters["body"].(string); ok {
		body = val
	}

	headers := ""
	if val, ok := parameters["headers"].(string); ok {
		headers = val
	}

	e.Logger.Info("空响应动作已执行: status_code=%d, content_type=%s, body_length=%d, headers=%s", statusCode, contentType, len(body), headers)
	return nil
}

func (e *NullResponseExecutor) Validate(parameters map[string]interface{}) error {
	// 所有参数都是可选的，有默认值
	return nil
}

func (e *NullResponseExecutor) GetType() string {
	return "null_response"
}

func (e *NullResponseExecutor) GetDescription() string {
	return "返回空响应或自定义响应"
}

// BypassProxyExecutor 绕过代理执行器
type BypassProxyExecutor struct {
	Logger interfaces.LogService
}

func (e *BypassProxyExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	timeoutMS := 30000 // 默认30秒
	if val, ok := parameters["timeout_ms"]; ok {
		switch v := val.(type) {
		case float64:
			timeoutMS = int(v)
		case int:
			timeoutMS = v
		case string:
			if t, err := strconv.Atoi(v); err == nil {
				timeoutMS = t
			}
		}
	} else if val, ok := parameters["timeout"]; ok {
		switch v := val.(type) {
		case float64:
			timeoutMS = int(v)
		case int:
			timeoutMS = v
		case string:
			if t, err := strconv.Atoi(v); err == nil {
				timeoutMS = t
			}
		}
	}

	e.Logger.Info("绕过代理动作已执行: timeout=%dms", timeoutMS)
	return nil
}

func (e *BypassProxyExecutor) Validate(parameters map[string]interface{}) error {
	// timeout 参数是可选的，有默认值
	return nil
}

func (e *BypassProxyExecutor) GetType() string {
	return "bypass_proxy"
}

func (e *BypassProxyExecutor) GetDescription() string {
	return "绕过代理直接连接"
}

// =============================================================================
// 数据结构定义
// =============================================================================

// 模块级别的日志器
var actionLogger = logger.GetActionLogger()

// ActionDefinition 动作定义（用于依赖注入架构）
type ActionDefinition struct {
	Name         string                 `json:"name"`
	Type         string                 `json:"type"`
	Enabled      bool                   `json:"enabled"`
	Parameters   map[string]interface{} `json:"parameters"`
	Description  string                 `json:"description"`
	Executor     Executor               `json:"-"`
	CreatedAt    time.Time              `json:"created_at"`
	LastExecuted time.Time              `json:"last_executed"`
	ExecuteCount int                    `json:"execute_count"`
	ErrorCount   int                    `json:"error_count"`
}

// ExecutionRequest 执行请求
type ExecutionRequest struct {
	ActionName string
	Context    context.Context
	Callback   func(error)
	Timestamp  time.Time
}

// ExecutionResult 执行结果
type ExecutionResult struct {
	ActionName string
	Success    bool
	Error      error
	Duration   time.Duration
	Timestamp  time.Time
}

// =============================================================================
// ActionManager 动作管理器
// =============================================================================

// ActionManager 动作管理器
type ActionManager struct {
	executors map[string]Executor
	logger    *logger.LoggerAdapter
}

// NewActionManager 创建新的动作管理器
func NewActionManager() *ActionManager {
	am := &ActionManager{
		executors: make(map[string]Executor),
		logger:    logger.GetLoggerAdapter(logger.ModuleAction),
	}

	// 注册内置执行器
	am.registerBuiltinExecutors()

	return am
}

// registerBuiltinExecutors 注册内置执行器
func (am *ActionManager) registerBuiltinExecutors() {
	// 创建LogService实例
	logService := &logServiceAdapter{adapter: am.logger}

	// 注册所有内置执行器
	am.RegisterExecutor(&LogExecutor{Logger: logService})
	am.RegisterExecutor(&BanIPExecutor{Logger: logService})
	am.RegisterExecutor(&BanDomainExecutor{Logger: logService})
	am.RegisterExecutor(&BlockRequestExecutor{Logger: logService})
	am.RegisterExecutor(&ModifyRequestExecutor{Logger: logService})
	am.RegisterExecutor(&ModifyResponseExecutor{Logger: logService})
	am.RegisterExecutor(&CacheResponseExecutor{Logger: logService})
	am.RegisterExecutor(&ScriptExecutor{Logger: logService})
	am.RegisterExecutor(&RetrySameExecutor{Logger: logService})
	am.RegisterExecutor(&RetryExecutor{Logger: logService})
	am.RegisterExecutor(&BanIPDomainExecutor{Logger: logService})
	am.RegisterExecutor(&SaveToPoolExecutor{Logger: logService})
	am.RegisterExecutor(&CacheExecutor{Logger: logService})
	am.RegisterExecutor(&RequestURLExecutor{Logger: logService})
	am.RegisterExecutor(&NullResponseExecutor{Logger: logService})
	am.RegisterExecutor(&BypassProxyExecutor{Logger: logService})
}

// logServiceAdapter 适配器，将LoggerAdapter转换为LogService接口
type logServiceAdapter struct {
	adapter *logger.LoggerAdapter
}

func (lsa *logServiceAdapter) Info(msg string, args ...interface{}) {
	lsa.adapter.Info(msg, args...)
}

func (lsa *logServiceAdapter) Warn(msg string, args ...interface{}) {
	lsa.adapter.Warn(msg, args...)
}

func (lsa *logServiceAdapter) Error(msg string, args ...interface{}) {
	lsa.adapter.Error(msg, args...)
}

func (lsa *logServiceAdapter) Debug(msg string, args ...interface{}) {
	lsa.adapter.Debug(msg, args...)
}

func (lsa *logServiceAdapter) Fatal(msg string, args ...interface{}) {
	lsa.adapter.Fatal(msg, args...)
}

func (lsa *logServiceAdapter) WithTraceID(traceID string) interfaces.LogService {
	return lsa // 简化实现
}

func (lsa *logServiceAdapter) WithFields(fields map[string]interface{}) interfaces.LogService {
	return lsa // 简化实现
}

func (lsa *logServiceAdapter) LogError(err error, msg string, args ...interface{}) {
	if msg != "" {
		lsa.adapter.Error(msg, args...)
	}
	if err != nil {
		lsa.adapter.Error("Error: %v", err)
	}
}

func (lsa *logServiceAdapter) GetLogger() interface{} {
	return lsa.adapter
}

// RegisterExecutor 注册执行器
func (am *ActionManager) RegisterExecutor(executor Executor) {
	am.executors[executor.GetType()] = executor
}

// GetExecutor 获取执行器
func (am *ActionManager) GetExecutor(actionType string) (Executor, bool) {
	executor, exists := am.executors[actionType]
	return executor, exists
}

// ExecuteAction 执行动作
func (am *ActionManager) ExecuteAction(ctx context.Context, actionType string, parameters map[string]interface{}) error {
	executor, exists := am.GetExecutor(actionType)
	if !exists {
		return errors.NewError(errors.ErrTypeAction, errors.ErrCodeActionNotFound, "未找到动作执行器: "+actionType)
	}

	// 验证参数
	if err := executor.Validate(parameters); err != nil {
		return err
	}

	// 执行动作
	return executor.Execute(ctx, parameters)
}

// ListExecutors 列出所有已注册的执行器
func (am *ActionManager) ListExecutors() []string {
	var types []string
	for actionType := range am.executors {
		types = append(types, actionType)
	}
	return types
}

// BuildActionFromConfig 从配置构建动作
func (am *ActionManager) BuildActionFromConfig(actionConfig common.ActionConfig) (*ActionDefinition, error) {
	actionType := actionConfig.Type
	if actionType == "" {
		return nil, errors.NewError(errors.ErrTypeAction, errors.ErrCodeMissingParameter, "动作配置缺少type字段")
	}

	executor, exists := am.GetExecutor(actionType)
	if !exists {
		return nil, errors.NewError(errors.ErrTypeAction, errors.ErrCodeActionNotFound, "未找到动作执行器: "+actionType)
	}

	// 使用ActionConfig的Params作为参数
	parameters := actionConfig.Params
	if parameters == nil {
		parameters = make(map[string]interface{})
	}

	// 验证参数
	if err := executor.Validate(parameters); err != nil {
		return nil, err
	}

	action := &ActionDefinition{
		Name:         actionType,
		Type:         actionType,
		Enabled:      true,
		Parameters:   parameters,
		Description:  executor.GetDescription(),
		Executor:     executor,
		CreatedAt:    time.Now(),
		LastExecuted: time.Time{},
		ExecuteCount: 0,
		ErrorCount:   0,
	}

	return action, nil
}

// Execute 执行动作定义
func (ad *ActionDefinition) Execute(ctx context.Context, req interface{}, resp interface{}, proxyManager interface{}) (*http.Response, error) {
	if !ad.Enabled {
		return nil, errors.NewError(errors.ErrTypeAction, errors.ErrCodeActionDisabled, "动作已禁用: "+ad.Name)
	}

	// 更新执行统计
	ad.LastExecuted = time.Now()
	ad.ExecuteCount++

	// 检查是否为HTTPExecutor，如果是则调用ExecuteHTTP方法
	if httpExecutor, ok := ad.Executor.(HTTPExecutor); ok {
		// 类型断言HTTP请求和响应对象
		var httpReq *http.Request
		var httpResp *http.Response

		if req != nil {
			if r, ok := req.(*http.Request); ok {
				httpReq = r
			}
		}

		if resp != nil {
			if r, ok := resp.(*http.Response); ok {
				httpResp = r
			}
		}

		// 调用HTTPExecutor的ExecuteHTTP方法
		_, modifiedResp, err := httpExecutor.ExecuteHTTP(ctx, ad.Parameters, httpReq, httpResp)
		if err != nil {
			ad.ErrorCount++
			return nil, err
		}

		// 如果响应被修改，返回修改后的响应
		if modifiedResp != nil && modifiedResp != httpResp {
			return modifiedResp, nil
		}

		// 对于modify_request类型的动作，不返回响应，让修改后的请求继续处理
		return nil, nil
	}

	// 对于非HTTPExecutor，使用原有的Execute方法
	err := ad.Executor.Execute(ctx, ad.Parameters)
	if err != nil {
		ad.ErrorCount++
		return nil, err
	}

	// 对于大多数动作，不返回HTTP响应，让原始响应继续
	return nil, nil
}
