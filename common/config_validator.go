package common

import (
	"encoding/json"
	"fmt"
	"net"
	"reflect"
	"strconv"
	"strings"

	"github.com/go-playground/validator/v10"
	"github.com/mubeng/mubeng/common/errors"
)

// ConfigValidator 配置验证器
type ConfigValidator struct {
	validator *validator.Validate
}

// NewConfigValidator 创建新的配置验证器
func NewConfigValidator() *ConfigValidator {
	v := validator.New()
	
	// 注册自定义验证规则
	v.RegisterValidation("duration_or_reboot", validateDurationOrReboot)
	v.RegisterValidation("action_type", validateActionType)
	v.RegisterValidation("trigger_type", validateTriggerType)
	v.RegisterValidation("ip_or_ip_port", validateIPOrIPPort)
	
	return &ConfigValidator{
		validator: v,
	}
}

// ValidateConfig 验证配置结构
func (cv *ConfigValidator) ValidateConfig(config *Config) error {
	if config == nil {
		return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldRequired,
			errors.ErrConfigFieldRequired.Message, "配置不能为空")
	}

	// 执行结构验证
	err := cv.validator.Struct(config)
	if err != nil {
		return cv.formatValidationError(err)
	}

	// 执行业务逻辑验证
	if err := cv.validateBusinessLogic(config); err != nil {
		return err
	}

	// 执行深度验证
	if err := cv.validateDeepLogic(config); err != nil {
		return err
	}

	return nil
}

// validateBusinessLogic 执行业务逻辑验证
func (cv *ConfigValidator) validateBusinessLogic(config *Config) error {
	// 验证动作序列引用
	for eventName, event := range config.Events {
		for _, match := range event.Matches {
			for _, action := range match.Actions {
				if action.Type == "" {
					return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldRequired,
						"事件中的动作类型不能为空", fmt.Sprintf("事件名称: %v", eventName))
				}
			}
		}
	}

	// 验证触发器条件
	for eventIndex, event := range config.Events {
		// 事件必须至少有一个条件、匹配规则或条件动作
		if len(event.Conditions) == 0 && len(event.Matches) == 0 && len(event.ConditionalActions) == 0 {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldRequired,
				"事件必须至少有一个条件、匹配规则或条件动作", fmt.Sprintf("事件索引: %d, 事件名称: %s", eventIndex, event.Name))
		}
	}

	return nil
}

// validateDeepLogic 执行深度业务逻辑验证
func (cv *ConfigValidator) validateDeepLogic(config *Config) error {
	// 验证代理文件路径
	if err := cv.validateProxyFile(config); err != nil {
		return err
	}

	// 验证端口配置
	if err := cv.validatePortConfiguration(config); err != nil {
		return err
	}

	// 验证认证配置
	if err := cv.validateAuthConfiguration(config); err != nil {
		return err
	}

	// 验证DNS配置
	if err := cv.validateDNSConfiguration(config); err != nil {
		return err
	}

	// 验证缓存配置
	if err := cv.validateCacheConfiguration(config); err != nil {
		return err
	}

	// 验证监控配置
	if err := cv.validateMonitoringConfiguration(config); err != nil {
		return err
	}

	// 验证modify动作配置
	if err := cv.validateModifyActionsConfiguration(config); err != nil {
		return err
	}

	return nil
}

// validateProxyFile 验证代理文件配置
func (cv *ConfigValidator) validateProxyFile(config *Config) error {
	if config.Global.ProxyFile == "" {
		return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigProxyFileEmpty,
			errors.ErrConfigProxyFileEmpty.Message, "请在 global.proxy_file 中指定有效的代理文件路径")
	}

	// 检查文件是否存在（如果是相对路径或绝对路径）
	if config.Global.ProxyFile != "" {
		// 这里不直接检查文件存在性，因为可能在运行时才创建
		// 但可以检查路径格式的合理性
		if len(config.Global.ProxyFile) > 255 {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				"代理文件路径过长", "路径长度不能超过255个字符")
		}
	}

	return nil
}

// validatePortConfiguration 验证端口配置
func (cv *ConfigValidator) validatePortConfiguration(config *Config) error {
	if config.Server != nil {
		if config.Server.Port < 1 || config.Server.Port > 65535 {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigPortInvalid,
				errors.ErrConfigPortInvalid.Message, fmt.Sprintf("端口必须在1-65535范围内，当前值: %d", config.Server.Port))
		}

		// 检查是否使用了系统保留端口
		if config.Server.Port < 1024 {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigPortReserved,
				errors.ErrConfigPortReserved.Message, fmt.Sprintf("端口 %d 是系统保留端口，建议使用1024以上的端口", config.Server.Port))
		}
	}

	return nil
}

// validateAuthConfiguration 验证认证配置
func (cv *ConfigValidator) validateAuthConfiguration(config *Config) error {
	if config.Security != nil && config.Security.Auth != nil {
		auth := config.Security.Auth

		// 验证认证类型
		if auth.Type == "" {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigAuthTypeEmpty,
				errors.ErrConfigAuthTypeEmpty.Message, "请在 security.auth.type 中指定认证类型 (none, basic, bearer, apikey)")
		}

		// 验证token过期时间格式
		if auth.TokenExpiry != "" {
			// 这里可以添加时间格式验证逻辑
			if len(auth.TokenExpiry) == 0 {
				return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
					"Token过期时间格式无效", "请检查 security.auth.token_expiry 的格式")
			}
		}
	}

	return nil
}

// validateDNSConfiguration 验证DNS配置
func (cv *ConfigValidator) validateDNSConfiguration(config *Config) error {
	if config.DNSService != nil {
		dns := config.DNSService

		// 验证DNS服务器配置
		if dns.Servers != nil {
			for name, server := range dns.Servers {
				if server == "" {
					return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigDNSServerEmpty,
						errors.ErrConfigDNSServerEmpty.Message, fmt.Sprintf("dns_service.servers['%s'] 不能为空", name))
				}

				// 简单的IP地址格式检查
				if !cv.isValidIPOrDomain(server) {
					return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigDNSServerInvalid,
						errors.ErrConfigDNSServerInvalid.Message, fmt.Sprintf("dns_service.servers['%s'] = '%s' 不是有效的IP地址或域名", name, server))
				}
			}
		}

		// 验证超时配置格式
		if dns.Timeout != "" {
			// 这里可以添加时间格式验证逻辑
			if len(dns.Timeout) == 0 {
				return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigDNSTimeoutInvalid,
					errors.ErrConfigDNSTimeoutInvalid.Message, "请检查 dns_service.timeout 的格式")
			}
		}

		// 验证重试次数
		if dns.Retries < 0 {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				"DNS重试次数无效", "dns_service.retries 不能为负数")
		}
	}

	return nil
}

// validateCacheConfiguration 验证缓存配置
func (cv *ConfigValidator) validateCacheConfiguration(config *Config) error {
	if config.Cache != nil {
		cache := config.Cache

		// 验证缓存类型
		if cache.Type == "" {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigCacheTypeEmpty,
				errors.ErrConfigCacheTypeEmpty.Message, "请在 cache.type 中指定缓存类型 (memory, redis, file)")
		}

		// 验证缓存大小
		if cache.Size < 0 {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigCacheSizeInvalid,
				errors.ErrConfigCacheSizeInvalid.Message, "cache.size 不能为负数")
		}

		// 验证TTL配置格式
		if cache.TTL != "" {
			// 这里可以添加时间格式验证逻辑
			if len(cache.TTL) == 0 {
				return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigCacheTTLInvalid,
					errors.ErrConfigCacheTTLInvalid.Message, "请检查 cache.ttl 的格式")
			}
		}

		// 验证清理间隔格式
		if cache.CleanupInterval != "" {
			// 这里可以添加时间格式验证逻辑
			if len(cache.CleanupInterval) == 0 {
				return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
					"缓存清理间隔格式无效", "请检查 cache.cleanup_interval 的格式")
			}
		}
	}

	return nil
}

// validateMonitoringConfiguration 验证监控配置
func (cv *ConfigValidator) validateMonitoringConfiguration(config *Config) error {
	if config.Monitoring != nil {
		monitoring := config.Monitoring

		// 验证监控端口
		if monitoring.Port < 1 || monitoring.Port > 65535 {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigMonitoringPortInvalid,
				errors.ErrConfigMonitoringPortInvalid.Message, fmt.Sprintf("monitoring.port 必须在1-65535范围内，当前值: %d", monitoring.Port))
		}

		// 验证监控路径
		if monitoring.Path == "" {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigMonitoringPathEmpty,
				errors.ErrConfigMonitoringPathEmpty.Message, "请在 monitoring.path 中指定监控路径")
		}
	}

	return nil
}

// isValidIPOrDomain 检查是否为有效的IP地址或域名
func (cv *ConfigValidator) isValidIPOrDomain(addr string) bool {
	// 简单的格式检查，实际项目中可以使用更严格的验证
	if addr == "" {
		return false
	}

	// 检查是否包含非法字符
	for _, char := range addr {
		if !((char >= 'a' && char <= 'z') ||
			 (char >= 'A' && char <= 'Z') ||
			 (char >= '0' && char <= '9') ||
			 char == '.' || char == '-' || char == ':') {
			return false
		}
	}

	return true
}

// formatValidationError 格式化验证错误信息
func (cv *ConfigValidator) formatValidationError(err error) error {
	if validationErrors, ok := err.(validator.ValidationErrors); ok {
		var errorMessages []string
		
		for _, fieldError := range validationErrors {
			fieldName := cv.getFieldName(fieldError)
			errorMsg := cv.getErrorMessage(fieldError)
			errorMessages = append(errorMessages, fmt.Sprintf("%s: %s", fieldName, errorMsg))
		}
		
		return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigValidationFailed, "配置验证失败", strings.Join(errorMessages, "\n"))
	}

	return errors.WrapError(err, errors.ErrTypeValidation, errors.ErrCodeConfigValidationFailed, "配置验证失败")
}

// getFieldName 获取字段名称
func (cv *ConfigValidator) getFieldName(fieldError validator.FieldError) string {
	namespace := fieldError.Namespace()
	// 移除结构体名称前缀
	if idx := strings.Index(namespace, "."); idx != -1 {
		return namespace[idx+1:]
	}
	return namespace
}

// getErrorMessage 获取错误信息
func (cv *ConfigValidator) getErrorMessage(fieldError validator.FieldError) string {
	switch fieldError.Tag() {
	case "required":
		return "此字段是必需的"
	case "ip":
		return "必须是有效的IP地址"
	case "ip_or_ip_port":
		return "必须是有效的IP地址或IP:端口格式"
	case "fqdn":
		return "必须是有效的域名"
	case "min":
		return fmt.Sprintf("最小值为 %s", fieldError.Param())
	case "max":
		return fmt.Sprintf("最大值为 %s", fieldError.Param())
	case "oneof":
		return fmt.Sprintf("必须是以下值之一: %s", fieldError.Param())
	case "dive":
		return "数组或切片元素验证失败"
	default:
		return fmt.Sprintf("验证失败: %s", fieldError.Tag())
	}
}

// 自定义验证函数

// validateDurationOrReboot 验证duration字段（可以是int或"reboot"字符串）
func validateDurationOrReboot(fl validator.FieldLevel) bool {
	value := fl.Field()
	
	switch value.Kind() {
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return value.Int() >= 0
	case reflect.String:
		return value.String() == "reboot"
	case reflect.Interface:
		// 处理interface{}类型
		if value.IsNil() {
			return false
		}
		actualValue := value.Elem()
		switch actualValue.Kind() {
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			return actualValue.Int() >= 0
		case reflect.String:
			return actualValue.String() == "reboot"
		}
	}
	
	return false
}

// validateActionType 验证动作类型
func validateActionType(fl validator.FieldLevel) bool {
	actionType := fl.Field().String()
	// 支持所有Executor实现的动作类型
	validTypes := []string{
		// 基础Executor动作类型
		"log", "banip", "ban_domain", "block_request",
		"modify_request", "modify_response", "cache_response", "script",
		// 从Action接口转换的Executor动作类型
		"retry_same", "retry", "banipdomain", "save_to_pool",
		"cache", "request_url", "null_response", "bypass_proxy",
	}

	for _, validType := range validTypes {
		if actionType == validType {
			return true
		}
	}
	return false
}

// validateTriggerType 验证触发器类型
func validateTriggerType(fl validator.FieldLevel) bool {
	triggerType := fl.Field().String()
	validTypes := []string{
		"status", "body", "max_request_time", "conn_time_out",
		"min_request_time", "url", "domain", "combined",
		"custom", "request_body", "request_header", "response_header",
	}

	for _, validType := range validTypes {
		if triggerType == validType {
			return true
		}
	}
	return false
}

// validateIPOrIPPort 验证IP地址或IP:端口格式
func validateIPOrIPPort(fl validator.FieldLevel) bool {
	value := fl.Field().String()
	if value == "" {
		return false
	}

	// 首先尝试直接解析为IP地址（包括IPv6）
	if net.ParseIP(value) != nil {
		return true
	}

	// 如果不是纯IP地址，尝试解析为IP:端口格式
	host, port, err := net.SplitHostPort(value)
	if err != nil {
		return false
	}

	// 验证IP地址
	if net.ParseIP(host) == nil {
		return false
	}

	// 验证端口号
	portNum, err := strconv.Atoi(port)
	if err != nil || portNum < 1 || portNum > 65535 {
		return false
	}

	return true
}

// validateModifyActionsConfiguration 验证modify动作配置
func (cv *ConfigValidator) validateModifyActionsConfiguration(config *Config) error {
	// 遍历所有事件中的动作
	for eventName, event := range config.Events {
		for matchIndex, match := range event.Matches {
			for actionIndex, action := range match.Actions {
				if action.Type == "modify_request" || action.Type == "modify_response" {
					if err := cv.validateModifyActionParameters(action, eventName, matchIndex, actionIndex); err != nil {
						return err
					}
				}
			}
		}
	}

	// 验证动作序列中的modify动作
	for actionName, actionSeq := range config.Actions {
		for seqIndex, action := range actionSeq.Sequence {
			if action.Type == "modify_request" || action.Type == "modify_response" {
				if err := cv.validateModifyActionParametersInSequence(action, actionName, seqIndex); err != nil {
					return err
				}
			}
		}
	}

	return nil
}

// validateModifyActionParameters 验证modify动作参数
func (cv *ConfigValidator) validateModifyActionParameters(action ActionConfig, eventName string, matchIndex, actionIndex int) error {
	actionType := action.Type

	// 验证body_config参数
	if bodyConfigRaw, exists := action.Params["body_config"]; exists {
		if err := cv.validateBodyConfigParameter(bodyConfigRaw, actionType,
			fmt.Sprintf("事件[%s].matches[%d].actions[%d]", eventName, matchIndex, actionIndex)); err != nil {
			return err
		}
	}

	// 验证headers参数
	if headersRaw, exists := action.Params["headers"]; exists {
		if err := cv.validateHeadersParameter(headersRaw, actionType,
			fmt.Sprintf("事件[%s].matches[%d].actions[%d]", eventName, matchIndex, actionIndex)); err != nil {
			return err
		}
	}

	// 验证remove_headers参数
	if removeHeadersRaw, exists := action.Params["remove_headers"]; exists {
		if err := cv.validateRemoveHeadersParameter(removeHeadersRaw, actionType,
			fmt.Sprintf("事件[%s].matches[%d].actions[%d]", eventName, matchIndex, actionIndex)); err != nil {
			return err
		}
	}

	// 验证status_code参数（仅对modify_response）
	if actionType == "modify_response" {
		if statusCodeRaw, exists := action.Params["status_code"]; exists {
			if err := cv.validateStatusCodeParameter(statusCodeRaw,
				fmt.Sprintf("事件[%s].matches[%d].actions[%d]", eventName, matchIndex, actionIndex)); err != nil {
				return err
			}
		}
	}

	// 验证keyword_operations参数
	if keywordOpsRaw, exists := action.Params["keyword_operations"]; exists {
		if err := cv.validateKeywordOperationsParameter(keywordOpsRaw, actionType,
			fmt.Sprintf("事件[%s].matches[%d].actions[%d]", eventName, matchIndex, actionIndex)); err != nil {
			return err
		}
	}

	return nil
}

// validateModifyActionParametersInSequence 验证动作序列中的modify动作参数
func (cv *ConfigValidator) validateModifyActionParametersInSequence(action ActionConfig, actionName string, seqIndex int) error {
	actionType := action.Type

	// 验证body_config参数
	if bodyConfigRaw, exists := action.Parameters["body_config"]; exists {
		if err := cv.validateBodyConfigParameter(bodyConfigRaw, actionType,
			fmt.Sprintf("动作序列[%s].sequence[%d]", actionName, seqIndex)); err != nil {
			return err
		}
	}

	// 验证headers参数
	if headersRaw, exists := action.Parameters["headers"]; exists {
		if err := cv.validateHeadersParameter(headersRaw, actionType,
			fmt.Sprintf("动作序列[%s].sequence[%d]", actionName, seqIndex)); err != nil {
			return err
		}
	}

	// 验证remove_headers参数
	if removeHeadersRaw, exists := action.Parameters["remove_headers"]; exists {
		if err := cv.validateRemoveHeadersParameter(removeHeadersRaw, actionType,
			fmt.Sprintf("动作序列[%s].sequence[%d]", actionName, seqIndex)); err != nil {
			return err
		}
	}

	// 验证status_code参数（仅对modify_response）
	if actionType == "modify_response" {
		if statusCodeRaw, exists := action.Parameters["status_code"]; exists {
			if err := cv.validateStatusCodeParameter(statusCodeRaw,
				fmt.Sprintf("动作序列[%s].sequence[%d]", actionName, seqIndex)); err != nil {
				return err
			}
		}
	}

	// 验证keyword_operations参数
	if keywordOpsRaw, exists := action.Parameters["keyword_operations"]; exists {
		if err := cv.validateKeywordOperationsParameter(keywordOpsRaw, actionType,
			fmt.Sprintf("动作序列[%s].sequence[%d]", actionName, seqIndex)); err != nil {
			return err
		}
	}

	return nil
}

// validateBodyConfigParameter 验证body_config参数
func (cv *ConfigValidator) validateBodyConfigParameter(bodyConfigRaw interface{}, actionType, location string) error {
	bodyConfigMap, ok := bodyConfigRaw.(map[string]interface{})
	if !ok {
		return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
			fmt.Sprintf("%s动作的body_config参数必须是对象格式", actionType),
			fmt.Sprintf("位置: %s", location))
	}

	// 验证content字段（必需）
	contentRaw, hasContent := bodyConfigMap["content"]
	if !hasContent {
		return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldRequired,
			fmt.Sprintf("%s动作的body_config.content字段是必需的", actionType),
			fmt.Sprintf("位置: %s，请添加content字段并提供内容", location))
	}
	content, ok := contentRaw.(string)
	if !ok {
		return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
			fmt.Sprintf("%s动作的body_config.content必须是字符串", actionType),
			fmt.Sprintf("位置: %s", location))
	}
	if len(content) == 0 {
		return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
			fmt.Sprintf("%s动作的body_config.content不能为空", actionType),
			fmt.Sprintf("位置: %s", location))
	}

	// 验证format字段
	var format string = "auto"
	if formatRaw, hasFormat := bodyConfigMap["format"]; hasFormat {
		var ok bool
		format, ok = formatRaw.(string)
		if !ok {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				fmt.Sprintf("%s动作的body_config.format必须是字符串", actionType),
				fmt.Sprintf("位置: %s", location))
		}

		// 验证format的有效值
		validFormats := []string{"json", "xml", "html", "text", "plain", "form", "urlencoded", "binary", "octet", "auto"}
		isValidFormat := false
		formatLower := strings.ToLower(format)
		for _, validFormat := range validFormats {
			if formatLower == validFormat {
				isValidFormat = true
				break
			}
		}
		if !isValidFormat {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				fmt.Sprintf("%s动作的body_config.format值无效: %s", actionType, format),
				fmt.Sprintf("位置: %s，支持的格式: %s", location, strings.Join(validFormats, ", ")))
		}
	}

	// 验证encoding字段
	if encodingRaw, hasEncoding := bodyConfigMap["encoding"]; hasEncoding {
		encoding, ok := encodingRaw.(string)
		if !ok {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				fmt.Sprintf("%s动作的body_config.encoding必须是字符串", actionType),
				fmt.Sprintf("位置: %s", location))
		}

		// 验证encoding的有效值
		validEncodings := []string{"utf-8", "base64"}
		isValidEncoding := false
		encodingLower := strings.ToLower(encoding)
		for _, validEncoding := range validEncodings {
			if encodingLower == validEncoding {
				isValidEncoding = true
				break
			}
		}
		if !isValidEncoding {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				fmt.Sprintf("%s动作的body_config.encoding值无效: %s", actionType, encoding),
				fmt.Sprintf("位置: %s，支持的编码: %s", location, strings.Join(validEncodings, ", ")))
		}
	}

	// 验证content_type字段
	if contentTypeRaw, hasContentType := bodyConfigMap["content_type"]; hasContentType {
		if _, ok := contentTypeRaw.(string); !ok {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				fmt.Sprintf("%s动作的body_config.content_type必须是字符串", actionType),
				fmt.Sprintf("位置: %s", location))
		}
	}

	// 验证auto_content_type字段
	if autoContentTypeRaw, hasAutoContentType := bodyConfigMap["auto_content_type"]; hasAutoContentType {
		if _, ok := autoContentTypeRaw.(bool); !ok {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				fmt.Sprintf("%s动作的body_config.auto_content_type必须是布尔值", actionType),
				fmt.Sprintf("位置: %s", location))
		}
	}

	// 根据format验证content内容（基本验证）
	if format != "auto" && format != "binary" && format != "octet" {
		if err := cv.validateContentFormat(content, format, actionType, location); err != nil {
			return err
		}
	}

	return nil
}

// validateHeadersParameter 验证headers参数
func (cv *ConfigValidator) validateHeadersParameter(headersRaw interface{}, actionType, location string) error {
	headersMap, ok := headersRaw.(map[string]interface{})
	if !ok {
		return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
			fmt.Sprintf("%s动作的headers参数必须是键值对格式", actionType),
			fmt.Sprintf("位置: %s", location))
	}

	for key, value := range headersMap {
		if key == "" {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				fmt.Sprintf("%s动作的headers中不能有空的头部名称", actionType),
				fmt.Sprintf("位置: %s", location))
		}
		if _, ok := value.(string); !ok {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				fmt.Sprintf("%s动作的headers[%s]值必须是字符串", actionType, key),
				fmt.Sprintf("位置: %s", location))
		}
	}

	return nil
}

// validateRemoveHeadersParameter 验证remove_headers参数
func (cv *ConfigValidator) validateRemoveHeadersParameter(removeHeadersRaw interface{}, actionType, location string) error {
	// 支持两种格式：[]string 和 []interface{}
	if removeHeadersSlice, ok := removeHeadersRaw.([]interface{}); ok {
		for i, header := range removeHeadersSlice {
			if headerStr, ok := header.(string); ok {
				if headerStr == "" {
					return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
						fmt.Sprintf("%s动作的remove_headers[%d]不能为空字符串", actionType, i),
						fmt.Sprintf("位置: %s", location))
				}
			} else {
				return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
					fmt.Sprintf("%s动作的remove_headers[%d]必须是字符串", actionType, i),
					fmt.Sprintf("位置: %s", location))
			}
		}
	} else if removeHeadersSlice, ok := removeHeadersRaw.([]string); ok {
		for i, header := range removeHeadersSlice {
			if header == "" {
				return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
					fmt.Sprintf("%s动作的remove_headers[%d]不能为空字符串", actionType, i),
					fmt.Sprintf("位置: %s", location))
			}
		}
	} else {
		return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
			fmt.Sprintf("%s动作的remove_headers参数必须是字符串数组", actionType),
			fmt.Sprintf("位置: %s", location))
	}

	return nil
}

// validateStatusCodeParameter 验证status_code参数
func (cv *ConfigValidator) validateStatusCodeParameter(statusCodeRaw interface{}, location string) error {
	statusCode, ok := statusCodeRaw.(int)
	if !ok {
		// 尝试从float64转换（JSON解析可能产生float64）
		if statusCodeFloat, ok := statusCodeRaw.(float64); ok {
			statusCode = int(statusCodeFloat)
		} else {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				"modify_response动作的status_code参数必须是整数",
				fmt.Sprintf("位置: %s", location))
		}
	}

	if statusCode < 100 || statusCode > 599 {
		return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
			fmt.Sprintf("modify_response动作的status_code必须在100-599范围内，当前值: %d", statusCode),
			fmt.Sprintf("位置: %s，请使用有效的HTTP状态码", location))
	}

	return nil
}

// validateContentFormat 验证内容格式
func (cv *ConfigValidator) validateContentFormat(content, format, actionType, location string) error {
	switch strings.ToLower(format) {
	case "json":
		var js interface{}
		if err := json.Unmarshal([]byte(content), &js); err != nil {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				fmt.Sprintf("%s动作的body_config.content不是有效的JSON格式", actionType),
				fmt.Sprintf("位置: %s，JSON错误: %v", location, err))
		}
	case "xml":
		if !strings.Contains(content, "<") || !strings.Contains(content, ">") {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				fmt.Sprintf("%s动作的body_config.content不是有效的XML格式", actionType),
				fmt.Sprintf("位置: %s，XML内容必须包含标签", location))
		}
	case "html":
		if !strings.Contains(content, "<") || !strings.Contains(content, ">") {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				fmt.Sprintf("%s动作的body_config.content不是有效的HTML格式", actionType),
				fmt.Sprintf("位置: %s，HTML内容必须包含标签", location))
		}
	}

	return nil
}

// validateKeywordOperationsParameter 验证keyword_operations参数
func (cv *ConfigValidator) validateKeywordOperationsParameter(keywordOpsRaw interface{}, actionType, location string) error {
	keywordOpsMap, ok := keywordOpsRaw.(map[string]interface{})
	if !ok {
		return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
			fmt.Sprintf("%s动作的keyword_operations参数必须是对象格式", actionType),
			fmt.Sprintf("位置: %s", location))
	}

	// 验证headers操作
	if headersRaw, hasHeaders := keywordOpsMap["headers"]; hasHeaders {
		if err := cv.validateHeaderKeywordOperations(headersRaw, actionType, location); err != nil {
			return err
		}
	}

	// 验证body操作
	if bodyRaw, hasBody := keywordOpsMap["body"]; hasBody {
		if err := cv.validateBodyKeywordOperations(bodyRaw, actionType, location); err != nil {
			return err
		}
	}

	return nil
}

// validateHeaderKeywordOperations 验证header关键字操作
func (cv *ConfigValidator) validateHeaderKeywordOperations(headersRaw interface{}, actionType, location string) error {
	headersList, ok := headersRaw.([]interface{})
	if !ok {
		return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
			fmt.Sprintf("%s动作的keyword_operations.headers必须是数组格式", actionType),
			fmt.Sprintf("位置: %s", location))
	}

	validOperations := []string{"add", "replace", "remove", "append"}
	validMatchTypes := []string{"exact", "contains", "wildcard", "regex"}
	validConditions := []string{"exists", "not_exists", "value_match"}

	for i, headerOpRaw := range headersList {
		headerOpMap, ok := headerOpRaw.(map[string]interface{})
		if !ok {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				fmt.Sprintf("%s动作的keyword_operations.headers[%d]必须是对象格式", actionType, i),
				fmt.Sprintf("位置: %s", location))
		}

		// 验证operation字段（必需）
		operationRaw, hasOperation := headerOpMap["operation"]
		if !hasOperation {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldRequired,
				fmt.Sprintf("%s动作的keyword_operations.headers[%d].operation字段是必需的", actionType, i),
				fmt.Sprintf("位置: %s", location))
		}
		operation, ok := operationRaw.(string)
		if !ok {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				fmt.Sprintf("%s动作的keyword_operations.headers[%d].operation必须是字符串", actionType, i),
				fmt.Sprintf("位置: %s", location))
		}

		// 验证operation的有效值
		isValidOperation := false
		for _, validOp := range validOperations {
			if operation == validOp {
				isValidOperation = true
				break
			}
		}
		if !isValidOperation {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				fmt.Sprintf("%s动作的keyword_operations.headers[%d].operation值无效: %s", actionType, i, operation),
				fmt.Sprintf("位置: %s，支持的操作: %s", location, strings.Join(validOperations, ", ")))
		}

		// 验证match_type字段
		if matchTypeRaw, hasMatchType := headerOpMap["match_type"]; hasMatchType {
			matchType, ok := matchTypeRaw.(string)
			if !ok {
				return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
					fmt.Sprintf("%s动作的keyword_operations.headers[%d].match_type必须是字符串", actionType, i),
					fmt.Sprintf("位置: %s", location))
			}

			// 验证match_type的有效值
			isValidMatchType := false
			for _, validMatchType := range validMatchTypes {
				if matchType == validMatchType {
					isValidMatchType = true
					break
				}
			}
			if !isValidMatchType {
				return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
					fmt.Sprintf("%s动作的keyword_operations.headers[%d].match_type值无效: %s", actionType, i, matchType),
					fmt.Sprintf("位置: %s，支持的匹配类型: %s", location, strings.Join(validMatchTypes, ", ")))
			}
		}

		// 验证condition字段
		if conditionRaw, hasCondition := headerOpMap["condition"]; hasCondition {
			condition, ok := conditionRaw.(string)
			if !ok {
				return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
					fmt.Sprintf("%s动作的keyword_operations.headers[%d].condition必须是字符串", actionType, i),
					fmt.Sprintf("位置: %s", location))
			}

			// 验证condition的有效值
			isValidCondition := false
			for _, validCondition := range validConditions {
				if condition == validCondition {
					isValidCondition = true
					break
				}
			}
			if !isValidCondition {
				return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
					fmt.Sprintf("%s动作的keyword_operations.headers[%d].condition值无效: %s", actionType, i, condition),
					fmt.Sprintf("位置: %s，支持的条件: %s", location, strings.Join(validConditions, ", ")))
			}
		}
	}

	return nil
}

// validateBodyKeywordOperations 验证body关键字操作
func (cv *ConfigValidator) validateBodyKeywordOperations(bodyRaw interface{}, actionType, location string) error {
	bodyList, ok := bodyRaw.([]interface{})
	if !ok {
		return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
			fmt.Sprintf("%s动作的keyword_operations.body必须是数组格式", actionType),
			fmt.Sprintf("位置: %s", location))
	}

	validOperations := []string{"replace", "remove", "add", "append"}
	validMatchTypes := []string{"exact", "contains", "wildcard", "regex"}
	validFormats := []string{"json", "xml", "html", "text", "auto"}

	for i, bodyOpRaw := range bodyList {
		bodyOpMap, ok := bodyOpRaw.(map[string]interface{})
		if !ok {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				fmt.Sprintf("%s动作的keyword_operations.body[%d]必须是对象格式", actionType, i),
				fmt.Sprintf("位置: %s", location))
		}

		// 验证operation字段（必需）
		operationRaw, hasOperation := bodyOpMap["operation"]
		if !hasOperation {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldRequired,
				fmt.Sprintf("%s动作的keyword_operations.body[%d].operation字段是必需的", actionType, i),
				fmt.Sprintf("位置: %s", location))
		}
		operation, ok := operationRaw.(string)
		if !ok {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				fmt.Sprintf("%s动作的keyword_operations.body[%d].operation必须是字符串", actionType, i),
				fmt.Sprintf("位置: %s", location))
		}

		// 验证operation的有效值
		isValidOperation := false
		for _, validOp := range validOperations {
			if operation == validOp {
				isValidOperation = true
				break
			}
		}
		if !isValidOperation {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				fmt.Sprintf("%s动作的keyword_operations.body[%d].operation值无效: %s", actionType, i, operation),
				fmt.Sprintf("位置: %s，支持的操作: %s", location, strings.Join(validOperations, ", ")))
		}

		// 验证pattern字段（必需）
		if patternRaw, hasPattern := bodyOpMap["pattern"]; hasPattern {
			if _, ok := patternRaw.(string); !ok {
				return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
					fmt.Sprintf("%s动作的keyword_operations.body[%d].pattern必须是字符串", actionType, i),
					fmt.Sprintf("位置: %s", location))
			}
		} else {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldRequired,
				fmt.Sprintf("%s动作的keyword_operations.body[%d].pattern字段是必需的", actionType, i),
				fmt.Sprintf("位置: %s", location))
		}

		// 验证replacement字段（replace和add操作需要）
		if operation == "replace" || operation == "add" || operation == "append" {
			if replacementRaw, hasReplacement := bodyOpMap["replacement"]; hasReplacement {
				if _, ok := replacementRaw.(string); !ok {
					return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
						fmt.Sprintf("%s动作的keyword_operations.body[%d].replacement必须是字符串", actionType, i),
						fmt.Sprintf("位置: %s", location))
				}
			} else {
				return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldRequired,
					fmt.Sprintf("%s动作的keyword_operations.body[%d].replacement字段对于%s操作是必需的", actionType, i, operation),
					fmt.Sprintf("位置: %s", location))
			}
		}

		// 验证format字段
		if formatRaw, hasFormat := bodyOpMap["format"]; hasFormat {
			format, ok := formatRaw.(string)
			if !ok {
				return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
					fmt.Sprintf("%s动作的keyword_operations.body[%d].format必须是字符串", actionType, i),
					fmt.Sprintf("位置: %s", location))
			}

			// 验证format的有效值
			isValidFormat := false
			for _, validFormat := range validFormats {
				if format == validFormat {
					isValidFormat = true
					break
				}
			}
			if !isValidFormat {
				return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
					fmt.Sprintf("%s动作的keyword_operations.body[%d].format值无效: %s", actionType, i, format),
					fmt.Sprintf("位置: %s，支持的格式: %s", location, strings.Join(validFormats, ", ")))
			}
		}

		// 验证match_type字段
		if matchTypeRaw, hasMatchType := bodyOpMap["match_type"]; hasMatchType {
			matchType, ok := matchTypeRaw.(string)
			if !ok {
				return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
					fmt.Sprintf("%s动作的keyword_operations.body[%d].match_type必须是字符串", actionType, i),
					fmt.Sprintf("位置: %s", location))
			}

			// 验证match_type的有效值
			isValidMatchType := false
			for _, validMatchType := range validMatchTypes {
				if matchType == validMatchType {
					isValidMatchType = true
					break
				}
			}
			if !isValidMatchType {
				return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
					fmt.Sprintf("%s动作的keyword_operations.body[%d].match_type值无效: %s", actionType, i, matchType),
					fmt.Sprintf("位置: %s，支持的匹配类型: %s", location, strings.Join(validMatchTypes, ", ")))
			}
		}
	}

	return nil
}